<div class="order-summary sticky-top" style="top: 20px;">
    <h5 class="mb-3">Order Summary</h5>



    <div class="d-flex justify-content-between mb-2">
        <span>Subtotal</span>
        <span id="SubtotalSummary">${{ number_format($subtotal, 2) }}</span>
    </div>

    <div class="d-flex justify-content-between mb-2">
        <span>Delivery</span>
        <span id="DeliverySummary">${{ number_format($deliveryCost, 2) }}</span>
    </div>

    <div class="d-flex justify-content-between mb-3">
        <span>Tax (13%)</span>
        <span id="TaxSummary">${{ number_format($tax, 2) }}</span>
    </div>

    <hr>

    <div class="d-flex justify-content-between mb-3">
        <strong>Total</strong>
        <strong class="h5" id="TotalSummary">${{ number_format($total, 2) }}</strong>
    </div>



    <button class="btn btn-dark w-100 mb-3" wire:click="proceedToCheckout" {{ $deliveryCost <= 0 ? 'disabled' : '' }}>
        Pay <span id="TotalBtnSummary"> ${{ number_format($total, 2) }} </span>
    </button>

    @if ($deliveryCost <= 0)
        <p class="text-muted small text-center">Please calculate delivery costs to continue.</p>
    @endif

    {{-- <div class="mt-4">
        <h6 class="mb-2">Included Services:</h6>
        <ul class="list-unstyled small text-muted">
            <li><i class="fas fa-check text-success me-2"></i>Free consultation included</li>
            <li><i class="fas fa-check text-success me-2"></i>Professional delivery team</li>
            <li><i class="fas fa-check text-success me-2"></i>Installation support available</li>
        </ul>
    </div> --}}
</div>

<script>
    // document.addEventListener('livewire:init', () => {
    //     Livewire.on('cart-updated-CartItems', (data) => {
    //         // Update summary values when cart changes
    //         // console.log(data)
    //         document.getElementById('SubtotalSummary').textContent = '$' + data[0].subtotal.toFixed(2);
    //         document.getElementById('TaxSummary').textContent = '$' + data[0].tax.toFixed(2);
    //         document.getElementById('TotalSummary').textContent = '$' + data[0].total.toFixed(2);
    //         document.getElementById('TotalBtnSummary').textContent = '$' + data[0].total.toFixed(2);
    //         // console.log(
    //         //     document.getElementById('SubtotalSummary').textContent,
    //         // document.getElementById('TaxSummary').textContent,
    //         // document.getElementById('TotalSummary').textContent,
    //         // document.getElementById('TotalBtnSummary').textContent,
    //         // )
    //     });
    // });
</script>   
