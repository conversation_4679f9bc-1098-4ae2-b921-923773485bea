/* Containers Management JS */
'use strict';

$(function () {
  console.log('🚀 Containers.js loaded');

  var dt_container_table = $('.datatables-users'),
    offCanvasForm = $('#offcanvasAddUser');

  console.log('📊 DataTable element found:', dt_container_table.length > 0);
  console.log('📝 OffCanvas form found:', offCanvasForm.length > 0);
  console.log('🔑 CSRF Token:', $('meta[name="csrf-token"]').attr('content'));
  console.log('🌐 Base URL:', typeof baseUrl !== 'undefined' ? baseUrl : 'UNDEFINED');

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  // Populate selects for size, category, warehouse, and type
  function populateSelect(url, $select, valueField, textField) {
    console.log('🔄 Populating select:', url);
    $.get(url, function (data) {
      console.log('✅ Select data received:', data);
      $select.empty();
      $select.append('<option value="">Select</option>');
      data.forEach(function (item) {
        $select.append(`<option value="${item[valueField]}">${item[textField]}</option>`);
      });
      $select.select2({
        placeholder: 'Select',
        dropdownParent: $select.parent()
      });
    }).fail(function(xhr, status, error) {
      console.error('❌ Failed to populate select:', url, {
        status: status,
        error: error,
        response: xhr.responseText
      });
    });
  }
  // Type select (static options)
  function populateTypeSelect($select) {
    $select.empty();
    $select.append('<option value="">Select</option>');
    $select.append('<option value="Rental">Rental</option>');
    $select.append('<option value="Purchase">Purchase</option>');
    $select.append('<option value="Both">Both</option>');
    $select.select2({
      placeholder: 'Select',
      dropdownParent: $select.parent()
    });
  }
  populateSelect(baseUrl + 'size', $('#add-container-size'), 'id', 'size');
  populateSelect(baseUrl + 'category', $('#add-container-category'), 'id', 'title');
  populateSelect(baseUrl + 'warehouses', $('#add-container-warehouse'), 'id', 'location');
  populateTypeSelect($('#add-container-type'));

  if (dt_container_table.length) {
    console.log('🚀 Initializing DataTable with baseUrl:', baseUrl);
    console.log('📡 AJAX URL:', baseUrl + 'dashboard/containers-list');

    var dt_container = dt_container_table.DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: baseUrl + 'dashboard/containers-list',
        error: function(xhr, error, code) {
          console.error('❌ DataTable AJAX Error:', {
            xhr: xhr,
            error: error,
            code: code,
            status: xhr.status,
            statusText: xhr.statusText,
            responseText: xhr.responseText
          });
        }
      },
      columns: [
        { data: '' },
        { data: 'id' },
        { data: 'title' },
        { data: 'size' },
        { data: 'category' },
        { data: 'type' },
        { data: 'rental_price' },
        { data: 'purchase_price' },
        { data: 'delivery_cost' },
        { data: 'warehouse' },
        { data: 'action' }
      ],
      columnDefs: [
        {
          className: 'control',
          searchable: false,
          orderable: false,
          responsivePriority: 2,
          targets: 0,
          render: function () { return ''; }
        },
        {
          searchable: false,
          orderable: false,
          targets: 1,
          render: function (data, type, full) {
            return `<span>${full.fake_id}</span>`;
          }
        },
        {
          targets: 2,
          responsivePriority: 4,
          render: function (data, type, full) {
            return `<span>${full.title}</span>`;
          }
        },
        {
          targets: 3,
          render: function (data, type, full) {
            return `<span>${full.size}ft</span>`;
          }
        },
        {
          targets: 4,
          render: function (data, type, full) {
            return `<span>${full.category}</span>`;
          }
        },
        {
          targets: 5,
          render: function (data, type, full) {
            // Capitalize first letter
            var typeText = full.type ? full.type.charAt(0).toUpperCase() + full.type.slice(1).toLowerCase() : '';
            return `<span>${typeText}</span>`;
          }
        },
        {
          targets: 6,
          render: function (data, type, full) {
            return `<span>$ ${full.rental_price}</span>`;
          }
        },
        {
          targets: 7,
          render: function (data, type, full) {
            return `<span>$ ${full.purchase_price}</span>`;
          }
        },
        {
          targets: 8,
          render: function (data, type, full) {
            return `<span>$ ${full.delivery_cost}</span>`;
          }
        },
        {
          targets: 9,
          render: function (data, type, full) {
            return `<span>${full.warehouse}</span>`;
          }
        },
        {
          targets: -1,
          title: 'Actions',
          searchable: false,
          orderable: false,
          render: function (data, type, full) {
            return (
              '<div class="d-flex align-items-center gap-50">' +
              `<button class="btn btn-sm btn-icon edit-record btn-text-secondary rounded-pill waves-effect" data-id="${full.id}" data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddUser"><i class="ti ti-edit"></i></button>` +
              `<button class="btn btn-sm btn-icon delete-record btn-text-secondary rounded-pill waves-effect" data-id="${full.id}"><i class="ti ti-trash"></i></button>` +
              '</div>'
            );
          }
        }
      ],
      order: [[1, 'asc']],
      dom:
        '<"row"' +
        '<"col-md-2"<"ms-n2"l>>' +
        '<"col-md-10"<"dt-action-buttons text-xl-end text-lg-start text-md-end text-start d-flex align-items-center justify-content-end flex-md-row flex-column mb-6 mb-md-0 mt-n6 mt-md-0"fB>>' +
        '>t' +
        '<"row"' +
        '<"col-sm-12 col-md-6"i>' +
        '<"col-sm-12 col-md-6"p>' +
        '>',
      lengthMenu: [7, 10, 20, 50, 70, 100],
      language: {
        sLengthMenu: '_MENU_',
        search: '',
        searchPlaceholder: 'Search Container',
        info: 'Displaying _START_ to _END_ of _TOTAL_ entries',
        paginate: {
          next: '<i class="ti ti-chevron-right ti-sm"></i>',
          previous: '<i class="ti ti-chevron-left ti-sm"></i>'
        }
      },
      buttons: [
        {
          text: '<i class="ti ti-plus me-0 me-sm-1 ti-xs"></i><span class="d-none d-sm-inline-block">Add New Container</span>',
          className: 'add-new btn btn-primary waves-effect waves-light',
          attr: {
            'data-bs-toggle': 'offcanvas',
            'data-bs-target': '#offcanvasAddUser'
          }
        }
      ]
    });

    console.log('✅ DataTable initialized successfully');
  } else {
    console.error('❌ DataTable element not found (.datatables-users)');
  }

  // Delete Record
  $(document).on('click', '.delete-record', function () {
    var container_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      customClass: {
        confirmButton: 'btn btn-primary me-3',
        cancelButton: 'btn btn-label-secondary'
      },
      buttonsStyling: false
    }).then(function (result) {
      if (result.value) {
        $.ajax({
          type: 'DELETE',
          url: `${baseUrl}dashboard/containers-list/${container_id}`,
          success: function () {
            dt_container.draw();
          },
          error: function (error) {
            console.log(error);
          }
        });
        Swal.fire({
          icon: 'success',
          title: 'Deleted!',
          text: 'The container has been deleted!',
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        Swal.fire({
          title: 'Cancelled',
          text: 'The container is not deleted!',
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      }
    });
  });

  // Edit Record
  $(document).on('click', '.edit-record', function () {
    var container_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }
    $('#offcanvasAddUserLabel').html('Edit Container');
    $.get(`${baseUrl}dashboard/containers-list/${container_id}/edit`, function (data) {
      $('#container_id').val(data.id);
      $('#add-container-title').val(data.title);
      $('#add-container-description').val(data.description);
      $('#add-container-size').val(data.size_id).trigger('change');
      $('#add-container-category').val(data.category_id).trigger('change');
      // Capitalize first letter for type select
      var typeValue = data.type ? data.type.charAt(0).toUpperCase() + data.type.slice(1).toLowerCase() : '';
      $('#add-container-type').val(typeValue).trigger('change');
      $('#add-container-rental-price').val(data.rental_price);
      $('#add-container-purchase-price').val(data.purchase_price);
      $('#add-container-delivery-cost').val(data.delivery_cost);
      $('#add-container-warehouse').val(data.warehouse_id).trigger('change');
    });
  });

  // Add New Container button
  $('.add-new').on('click', function () {
    $('#container_id').val('');
    $('#offcanvasAddUserLabel').html('Add Container');
    $('#add-container-title').val('');
    $('#add-container-description').val('');
    $('#add-container-size').val('').trigger('change');
    $('#add-container-category').val('').trigger('change');
    $('#add-container-type').val('').trigger('change');
    $('#add-container-rental-price').val('');
    $('#add-container-purchase-price').val('');
    $('#add-container-delivery-cost').val('');
    $('#add-container-warehouse').val('').trigger('change');
  });

  // Image preview
  $('#add-container-image').on('change', function() {
    var input = this;
    if (input.files && input.files[0]) {
      var reader = new FileReader();
      reader.onload = function(e) {
        $('#container-image-preview').html('<img src="' + e.target.result + '" style="max-width:100px; max-height:100px; margin-top:10px;" />');
      }
      reader.readAsDataURL(input.files[0]);
    }
  });

  // Form validation and submit
  const addNewContainerForm = document.getElementById('addNewUserForm');
  const fv = FormValidation.formValidation(addNewContainerForm, {
    fields: {
      title: {
        validators: {
          notEmpty: {
            message: 'Please enter container title'
          }
        }
      },
      size_id: {
        validators: {
          notEmpty: {
            message: 'Please select a size'
          }
        }
      },
      category_id: {
        validators: {
          notEmpty: {
            message: 'Please select a category'
          }
        }
      },
      type: {
        validators: {
          notEmpty: {
            message: 'Please select a type'
          }
        }
      },
      rental_price: {
        validators: {
          notEmpty: {
            message: 'Please enter rental price'
          },
          numeric: {
            message: 'Rental price must be a number'
          }
        }
      },
      purchase_price: {
        validators: {
          notEmpty: {
            message: 'Please enter purchase price'
          },
          numeric: {
            message: 'Purchase price must be a number'
          }
        }
      },
      delivery_cost: {
        validators: {
          notEmpty: {
            message: 'Please enter delivery cost'
          },
          numeric: {
            message: 'Delivery cost must be a number'
          }
        }
      },
      warehouse_id: {
        validators: {
          notEmpty: {
            message: 'Please select a warehouse'
          }
        }
      }
    },
    plugins: {
      trigger: new FormValidation.plugins.Trigger(),
      bootstrap5: new FormValidation.plugins.Bootstrap5({
        eleValidClass: '',
        rowSelector: function (field, ele) {
          return '.mb-6';
        }
      }),
      submitButton: new FormValidation.plugins.SubmitButton(),
      autoFocus: new FormValidation.plugins.AutoFocus()
    }
  }).on('core.form.valid', function () {
    // Just trigger the form submit (handled below)
    $('#addNewUserForm').trigger('submit');
  });

  // Always attach the submit handler
  $('#addNewUserForm').off('submit').on('submit', function(e) {
    e.preventDefault();
    var form = $(this)[0];
    var formData = new FormData(form);
    $.ajax({
      url: baseUrl + 'dashboard/containers-list',
      type: 'POST',
      data: formData,
      processData: false,
      contentType: false,
      success: function (status) {
        dt_container.draw();
        offCanvasForm.offcanvas('hide');
        Swal.fire({
          icon: 'success',
          title: `Successfully ${status}!`,
          text: `Container ${status} Successfully.`,
          customClass: { confirmButton: 'btn btn-success' }
        });
      },
      error: function (err) {
        offCanvasForm.offcanvas('hide');
        Swal.fire({
          title: 'Duplicate Entry!',
          text: 'Container title should be unique.',
          icon: 'error',
          customClass: { confirmButton: 'btn btn-success' }
        });
      }
    });
  });

  // Clear form data when offcanvas hidden
  offCanvasForm.on('hidden.bs.offcanvas', function () {
    fv.resetForm(true);
  });
});
