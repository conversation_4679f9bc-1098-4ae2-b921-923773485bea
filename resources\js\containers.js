/* Containers Management JS */
'use strict';

$(function () {
  console.log('🚀 Containers.js loaded');

  var dt_container_table = $('.datatables-containers'),
    offCanvasForm = $('#offcanvasAddContainer');

  console.log('📊 DataTable element found:', dt_container_table.length > 0);
  console.log('📝 OffCanvas form found:', offCanvasForm.length > 0);
  console.log('🔑 CSRF Token:', $('meta[name="csrf-token"]').attr('content'));
  console.log('🌐 Base URL:', typeof baseUrl !== 'undefined' ? baseUrl : 'UNDEFINED');

  // Check if baseUrl is defined
  if (typeof baseUrl === 'undefined') {
    console.error('❌ baseUrl is not defined. Please ensure it is defined in your layout.');
    return;
  }

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  // Initialize Select2 for all select elements
  function initializeSelect2($element, placeholder = 'Select') {
    $element.select2({
      placeholder: placeholder,
      dropdownParent: $element.parent(),
      allowClear: true
    });
  }

  // Populate selects for size, category, warehouse
  function populateSelect(url, $select, valueField, textField, placeholder = 'Select') {
    console.log('🔄 Populating select:', url);
    
    return $.get(url)
      .done(function (data) {
        console.log('✅ Select data received:', data);
        $select.empty();
        $select.append(`<option value="">${placeholder}</option>`);
        
        if (Array.isArray(data) && data.length > 0) {
          data.forEach(function (item) {
            if (item[valueField] !== undefined && item[textField] !== undefined) {
              $select.append(`<option value="${item[valueField]}">${item[textField]}</option>`);
            }
          });
        }
        
        initializeSelect2($select, placeholder);
      })
      .fail(function(xhr, status, error) {
        console.error('❌ Failed to populate select:', url, {
          status: status,
          error: error,
          response: xhr.responseText
        });
        
        // Initialize empty select with Select2
        $select.empty();
        $select.append(`<option value="">${placeholder}</option>`);
        initializeSelect2($select, placeholder);
      });
  }

  // Initialize all selects
  populateSelect(baseUrl + 'size', $('#add-container-size'), 'id', 'size', 'Select Size');
  populateSelect(baseUrl + 'category', $('#add-container-category'), 'id', 'title', 'Select Category');
  populateSelect(baseUrl + 'warehouses', $('#add-container-warehouse'), 'id', 'location', 'Select Warehouse');
  
  // Initialize type select
  initializeSelect2($('#add-container-type'), 'Select Type');

  // Initialize DataTable
  if (dt_container_table.length) {
    console.log('🚀 Initializing DataTable with baseUrl:', baseUrl);
    console.log('📡 AJAX URL:', baseUrl + 'dashboard/containers-list');

    try {
      var dt_container = dt_container_table.DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
          url: baseUrl + 'dashboard/containers-list',
          type: 'GET',
          error: function(xhr, error, code) {
            console.error('❌ DataTable AJAX Error:', {
              xhr: xhr,
              error: error,
              code: code,
              status: xhr.status,
              statusText: xhr.statusText,
              responseText: xhr.responseText
            });
          }
        },
        columns: [
          { 
            data: null, 
            name: 'control',
            orderable: false, 
            searchable: false,
            className: 'control',
            defaultContent: ''
          },
          { 
            data: 'id', 
            name: 'id',
            title: 'ID'
          },
          { 
            data: 'title', 
            name: 'title',
            title: 'Title'
          },
          { 
            data: 'size', 
            name: 'size',
            title: 'Size'
          },
          { 
            data: 'category', 
            name: 'category',
            title: 'Category'
          },
          { 
            data: 'type', 
            name: 'type',
            title: 'Type'
          },
          { 
            data: 'rental_price', 
            name: 'rental_price',
            title: 'Rental Price'
          },
          { 
            data: 'purchase_price', 
            name: 'purchase_price',
            title: 'Purchase Price'
          },
          { 
            data: 'delivery_cost', 
            name: 'delivery_cost',
            title: 'Delivery Cost'
          },
          { 
            data: 'warehouse', 
            name: 'warehouse',
            title: 'Warehouse'
          },
          { 
            data: null, 
            name: 'actions',
            orderable: false, 
            searchable: false,
            title: 'Actions'
          }
        ],
        columnDefs: [
          {
            targets: 0, // control column
            className: 'control',
            responsivePriority: 1,
            render: function () {
              return '';
            }
          },
          {
            targets: 1, // ID column
            render: function (data, type, full) {
              return `<span class="fw-medium">${full.id || ''}</span>`;
            }
          },
          {
            targets: 2, // Title column
            responsivePriority: 2,
            render: function (data, type, full) {
              return `<span class="fw-medium">${full.title || ''}</span>`;
            }
          },
          {
            targets: 3, // Size column
            render: function (data, type, full) {
              return `<span>${full.size || ''}${full.size ? 'ft' : ''}</span>`;
            }
          },
          {
            targets: 4, // Category column
            render: function (data, type, full) {
              return `<span>${full.category || ''}</span>`;
            }
          },
          {
            targets: 5, // Type column
            render: function (data, type, full) {
              if (!full.type) return '<span>-</span>';
              var typeText = full.type.charAt(0).toUpperCase() + full.type.slice(1).toLowerCase();
              var badgeClass = full.type.toLowerCase() === 'rental' ? 'bg-info' : 
                              full.type.toLowerCase() === 'purchase' ? 'bg-success' : 'bg-primary';
              return `<span class="badge ${badgeClass}">${typeText}</span>`;
            }
          },
          {
            targets: 6, // Rental Price column
            render: function (data, type, full) {
              return `<span>$${full.rental_price || '0.00'}</span>`;
            }
          },
          {
            targets: 7, // Purchase Price column
            render: function (data, type, full) {
              return `<span>$${full.purchase_price || '0.00'}</span>`;
            }
          },
          {
            targets: 8, // Delivery Cost column
            render: function (data, type, full) {
              return `<span>$${full.delivery_cost || '0.00'}</span>`;
            }
          },
          {
            targets: 9, // Warehouse column
            render: function (data, type, full) {
              return `<span>${full.warehouse || ''}</span>`;
            }
          },
          {
            targets: -1, // Actions column
            responsivePriority: 1,
            render: function (data, type, full) {
              return (
                '<div class="d-flex align-items-center gap-50">' +
                `<button class="btn btn-sm btn-icon edit-record btn-text-secondary rounded-pill waves-effect" data-id="${full.id}" data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddContainer" title="Edit">` +
                '<i class="ti ti-edit"></i>' +
                '</button>' +
                `<button class="btn btn-sm btn-icon delete-record btn-text-secondary rounded-pill waves-effect" data-id="${full.id}" title="Delete">` +
                '<i class="ti ti-trash"></i>' +
                '</button>' +
                '</div>'
              );
            }
          }
        ],
        order: [[1, 'desc']],
        dom:
          '<"row"' +
          '<"col-md-2"<"ms-n2"l>>' +
          '<"col-md-10"<"dt-action-buttons text-xl-end text-lg-start text-md-end text-start d-flex align-items-center justify-content-end flex-md-row flex-column mb-6 mb-md-0 mt-n6 mt-md-0"fB>>' +
          '>t' +
          '<"row"' +
          '<"col-sm-12 col-md-6"i>' +
          '<"col-sm-12 col-md-6"p>' +
          '>',
        lengthMenu: [7, 10, 20, 50, 70, 100],
        language: {
          sLengthMenu: '_MENU_',
          search: '',
          searchPlaceholder: 'Search Container',
          info: 'Displaying _START_ to _END_ of _TOTAL_ entries',
          paginate: {
            next: '<i class="ti ti-chevron-right ti-sm"></i>',
            previous: '<i class="ti ti-chevron-left ti-sm"></i>'
          }
        },
        buttons: [
          {
            text: '<i class="ti ti-plus me-0 me-sm-1 ti-xs"></i><span class="d-none d-sm-inline-block">Add New Container</span>',
            className: 'add-new btn btn-primary waves-effect waves-light',
            attr: {
              'data-bs-toggle': 'offcanvas',
              'data-bs-target': '#offcanvasAddContainer'
            }
          }
        ]
      });

      console.log('✅ DataTable initialized successfully');

    } catch (error) {
      console.error('❌ DataTable initialization failed:', error);
    }
  } else {
    console.error('❌ DataTable element not found (.datatables-containers)');
  }

  // Delete Record
  $(document).on('click', '.delete-record', function (e) {
    e.preventDefault();
    var container_id = $(this).data('id');
    var dtrModal = $('.dtr-bs-modal.show');
    
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }
    
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'btn btn-primary me-3 waves-effect waves-light',
        cancelButton: 'btn btn-label-secondary waves-effect'
      },
      buttonsStyling: false
    }).then(function (result) {
      if (result.value) {
        $.ajax({
          type: 'DELETE',
          url: `${baseUrl}dashboard/containers-list/${container_id}`,
          success: function (response) {
            dt_container.draw();
            Swal.fire({
              icon: 'success',
              title: 'Deleted!',
              text: 'The container has been deleted!',
              customClass: {
                confirmButton: 'btn btn-success waves-effect'
              }
            });
          },
          error: function (xhr, status, error) {
            console.error('Delete error:', error);
            Swal.fire({
              title: 'Error!',
              text: 'There was an error deleting the container.',
              icon: 'error',
              customClass: {
                confirmButton: 'btn btn-danger waves-effect'
              }
            });
          }
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        Swal.fire({
          title: 'Cancelled',
          text: 'The container is safe!',
          icon: 'info',
          customClass: {
            confirmButton: 'btn btn-success waves-effect'
          }
        });
      }
    });
  });

  // Edit Record
  $(document).on('click', '.edit-record', function (e) {
    e.preventDefault();
    var container_id = $(this).data('id');
    var dtrModal = $('.dtr-bs-modal.show');
    
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }
    
    $('#offcanvasAddContainerLabel').html('Edit Container');
    
    $.get(`${baseUrl}dashboard/containers-list/${container_id}/edit`)
      .done(function (data) {
        console.log('Edit data received:', data);
        
        $('#container_id').val(data.id || '');
        $('#add-container-title').val(data.title || '');
        $('#add-container-description').val(data.description || '');
        $('#add-container-size').val(data.size_id || '').trigger('change');
        $('#add-container-category').val(data.category_id || '').trigger('change');
        $('#add-container-type').val(data.type || '').trigger('change');
        $('#add-container-rental-price').val(data.rental_price || '');
        $('#add-container-purchase-price').val(data.purchase_price || '');
        $('#add-container-delivery-cost').val(data.delivery_cost || '');
        $('#add-container-warehouse').val(data.warehouse_id || '').trigger('change');
        
        // Clear image preview
        $('#container-image-preview').empty();
      })
      .fail(function (xhr, status, error) {
        console.error('Edit fetch error:', error);
        Swal.fire({
          title: 'Error!',
          text: 'Could not load container data.',
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-danger waves-effect'
          }
        });
      });
  });

  // Add New Container button
  $('.add-new').on('click', function () {
    $('#offcanvasAddContainerLabel').html('Add Container');
    clearForm();
  });

  // Clear form function
  function clearForm() {
    $('#container_id').val('');
    $('#add-container-title').val('');
    $('#add-container-description').val('');
    $('#add-container-size').val('').trigger('change');
    $('#add-container-category').val('').trigger('change');
    $('#add-container-type').val('').trigger('change');
    $('#add-container-rental-price').val('');
    $('#add-container-purchase-price').val('');
    $('#add-container-delivery-cost').val('');
    $('#add-container-warehouse').val('').trigger('change');
    $('#container-image-preview').empty();
    $('#add-container-image').val('');
  }

  // Image preview
  $('#add-container-image').on('change', function() {
    var input = this;
    var $preview = $('#container-image-preview');
    
    $preview.empty();
    
    if (input.files && input.files[0]) {
      var reader = new FileReader();
      reader.onload = function(e) {
        $preview.html(`
          <div class="mt-2">
            <img src="${e.target.result}" class="img-thumbnail" style="max-width: 100px; max-height: 100px;" alt="Preview" />
          </div>
        `);
      };
      reader.readAsDataURL(input.files[0]);
    }
  });

  // Form validation - Initialize after a delay to ensure DOM is ready
  var fv = null;
  
  function initializeFormValidation() {
    const addNewContainerForm = document.getElementById('addNewContainerForm');
    
    if (!addNewContainerForm) {
      console.warn('⚠️ Form element not found');
      return;
    }
    
    // Check if all required form elements exist
    const requiredElements = [
      'add-container-title',
      'add-container-size', 
      'add-container-category',
      'add-container-type',
      'add-container-rental-price',
      'add-container-purchase-price',
      'add-container-delivery-cost',
      'add-container-warehouse'
    ];
    
    var missingElements = [];
    requiredElements.forEach(function(id) {
      if (!document.getElementById(id)) {
        missingElements.push(id);
      }
    });
    
    if (missingElements.length > 0) {
      console.warn('⚠️ Missing form elements:', missingElements);
      return;
    }
    
    // Initialize FormValidation only if all elements exist and FormValidation is available
    if (typeof FormValidation !== 'undefined' && FormValidation.formValidation) {
      try {
        fv = FormValidation.formValidation(addNewContainerForm, {
          fields: {
            title: {
              validators: {
                notEmpty: {
                  message: 'Please enter container title'
                }
              }
            },
            size_id: {
              validators: {
                notEmpty: {
                  message: 'Please select a size'
                }
              }
            },
            category_id: {
              validators: {
                notEmpty: {
                  message: 'Please select a category'
                }
              }
            },
            type: {
              validators: {
                notEmpty: {
                  message: 'Please select a type'
                }
              }
            },
            rental_price: {
              validators: {
                notEmpty: {
                  message: 'Please enter rental price'
                },
                numeric: {
                  message: 'Rental price must be a number'
                }
              }
            },
            purchase_price: {
              validators: {
                notEmpty: {
                  message: 'Please enter purchase price'
                },
                numeric: {
                  message: 'Purchase price must be a number'
                }
              }
            },
            delivery_cost: {
              validators: {
                notEmpty: {
                  message: 'Please enter delivery cost'
                },
                numeric: {
                  message: 'Delivery cost must be a number'
                }
              }
            },
            warehouse_id: {
              validators: {
                notEmpty: {
                  message: 'Please select a warehouse'
                }
              }
            }
          },
          plugins: {
            trigger: new FormValidation.plugins.Trigger(),
            bootstrap5: new FormValidation.plugins.Bootstrap5({
              eleValidClass: '',
              rowSelector: function (field, ele) {
                return '.mb-6';
              }
            }),
            submitButton: new FormValidation.plugins.SubmitButton(),
            autoFocus: new FormValidation.plugins.AutoFocus()
          }
        }).on('core.form.valid', function () {
          submitForm();
        });
        
        console.log('✅ FormValidation initialized successfully');
      } catch (error) {
        console.error('❌ FormValidation initialization failed:', error);
        fv = null;
      }
    } else {
      console.warn('⚠️ FormValidation not available');
    }
  }

  // Submit form function
  function submitForm() {
    var form = $('#addNewContainerForm')[0];
    var formData = new FormData(form);
    var container_id = $('#container_id').val();
    var method = 'POST';
    var url = baseUrl + 'dashboard/containers-list';
    
    // Add method spoofing for Laravel if updating
    if (container_id) {
      formData.append('_method', 'PUT');
      url += '/' + container_id;
    }

    $.ajax({
      url: url,
      type: method,
      data: formData,
      processData: false,
      contentType: false,
      beforeSend: function() {
        $('.data-submit').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status"></span> Loading...');
      },
      success: function (response) {
        if (typeof dt_container !== 'undefined') {
          dt_container.draw();
        }
        offCanvasForm.offcanvas('hide');
        
        var action = container_id ? 'Updated' : 'Added';
        Swal.fire({
          icon: 'success',
          title: `Successfully ${action}!`,
          text: `Container ${action} Successfully.`,
          customClass: { 
            confirmButton: 'btn btn-success waves-effect' 
          }
        });
      },
      error: function (xhr, status, error) {
        console.error('Form submit error:', error);
        
        var errorMessage = 'An error occurred while saving the container.';
        if (xhr.responseJSON && xhr.responseJSON.message) {
          errorMessage = xhr.responseJSON.message;
        } else if (xhr.status === 422) {
          errorMessage = 'Please check your input data.';
        }
        
        Swal.fire({
          title: 'Error!',
          text: errorMessage,
          icon: 'error',
          customClass: { 
            confirmButton: 'btn btn-danger waves-effect' 
          }
        });
      },
      complete: function() {
        $('.data-submit').prop('disabled', false).html('Submit');
      }
    });
  }

  // Form submit handler - Always attach this regardless of validation
  $(document).on('submit', '#addNewContainerForm', function(e) {
    e.preventDefault();
    
    // If FormValidation is active, let it handle validation
    if (fv) {
      return; // Let FormValidation handle the submit
    }
    
    // Otherwise, submit directly
    submitForm();
  });

  // Initialize form validation after DOM is fully ready
  setTimeout(function() {
    initializeFormValidation();
  }, 100);

  // Clear form data when offcanvas hidden
  offCanvasForm.on('hidden.bs.offcanvas', function () {
    if (fv && typeof fv.resetForm === 'function') {
      try {
        fv.resetForm(true);
      } catch (error) {
        console.warn('Could not reset form validation:', error);
      }
    }
    clearForm();
  });
});