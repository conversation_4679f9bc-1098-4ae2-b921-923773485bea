// Containers Management JS - Bootstrap Native Validation
'use strict';

$(function () {
  var dt_container_table = $('.datatables-containers'),
    offCanvasForm = $('#offcanvasAddContainer');

  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  // Bootstrap form validation
  function initBootstrapValidation() {
    // Enable Bootstrap validation styles
    var forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function (form) {
      form.addEventListener('submit', function (event) {
        event.preventDefault();
        event.stopPropagation();
        
        if (form.checkValidity()) {
          submitForm();
        } else {
          // Show validation errors
          form.classList.add('was-validated');
        }
      }, false);
    });
  }

  // Submit form
  function submitForm() {
    var form = $('#addNewContainerForm')[0];
    var formData = new FormData(form);
    var container_id = $('#container_id').val();
    var url = baseUrl + 'dashboard/containers-list';
    var method = 'POST';

    if (container_id) {
      formData.append('_method', 'PUT');
      url += '/' + container_id;
    }

    $('.data-submit').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2"></span>Saving...');

    $.ajax({
      url: url,
      type: method,
      data: formData,
      processData: false,
      contentType: false,
      success: function(response) {
        dt_container.draw();
        offCanvasForm.offcanvas('hide');
        
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: container_id ? 'Container updated successfully!' : 'Container added successfully!',
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
        
        resetForm();
      },
      error: function(xhr) {
        var errorMessage = 'An error occurred while saving the container.';
        if (xhr.responseJSON && xhr.responseJSON.message) {
          errorMessage = xhr.responseJSON.message;
        }
        
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: errorMessage,
          customClass: {
            confirmButton: 'btn btn-danger'
          }
        });
      },
      complete: function() {
        $('.data-submit').prop('disabled', false).html('Submit');
      }
    });
  }

  // Reset form
  function resetForm() {
    var form = document.getElementById('addNewContainerForm');
    form.reset();
    form.classList.remove('was-validated');
    $('#container_id').val('');
  }

  // Initialize validation
  initBootstrapValidation();

  // Rest of your existing code...
  // (DataTable, delete, edit functionality remains the same)
  
  if (dt_container_table.length) {
    var dt_container = dt_container_table.DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: baseUrl + 'dashboard/containers-list'
      },
      columns: [
        { data: null, orderable: false, searchable: false },
        { data: 'id' },
        { data: 'title' },
        { data: 'size' },
        { data: 'category' },
        { data: 'type' },
        { data: 'rental_price' },
        { data: 'purchase_price' },
        { data: 'delivery_cost' },
        { data: 'warehouse' },
        { data: null, orderable: false, searchable: false }
      ],
      columnDefs: [
        {
          className: 'control',
          targets: 0,
          render: function () { return ''; }
        },
        {
          targets: 1,
          visible: false
        },
        {
          targets: -1,
          render: function (data, type, full) {
            return `
              <div class="d-flex align-items-center gap-2">
                <button class="btn btn-sm btn-icon edit-record btn-text-secondary" data-id="${full.id}" data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddContainer">
                  <i class="ti ti-edit"></i>
                </button>
                <button class="btn btn-sm btn-icon delete-record btn-text-secondary" data-id="${full.id}">
                  <i class="ti ti-trash"></i>
                </button>
              </div>
            `;
          }
        }
      ],
      order: [[2, 'asc']],
      dom: '<"card-header border-bottom p-1"<"head-label"><"dt-action-buttons text-end pt-0"B>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
      buttons: [
        {
          text: '<i class="ti ti-plus me-0 me-sm-1 ti-xs"></i><span class="d-none d-sm-inline-block">Add New Container</span>',
          className: 'add-new btn btn-primary waves-effect waves-light',
          attr: {
            'data-bs-toggle': 'offcanvas',
            'data-bs-target': '#offcanvasAddContainer'
          }
        }
      ]
    });
  }

  // Event handlers (same as before)
  $(document).on('click', '.delete-record', function() {
    // Delete logic here
  });

  $(document).on('click', '.edit-record', function() {
    // Edit logic here
  });

  $('.add-new').on('click', function() {
    $('#offcanvasAddContainerLabel').html('Add Container');
    resetForm();
  });
});