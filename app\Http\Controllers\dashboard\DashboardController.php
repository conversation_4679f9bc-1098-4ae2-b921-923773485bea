<?php

namespace App\Http\Controllers\dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Str;
use App\Models\Size;
use App\Models\Category;
use App\Models\Warehouse;
use App\Models\Container;
use App\Models\Customer;

class DashboardController extends Controller
{
  public function index()
  {
    return view('dashboard.index');
  }

  public function customers()
  {
    // dd('UserManagement');
    $users = User::all();
    $userCount = $users->count();
    $verified = User::whereNotNull('email_verified_at')->get()->count();
    $notVerified = User::whereNull('email_verified_at')->get()->count();
    $usersUnique = $users->unique(['email']);
    $userDuplicates = $users->diff($usersUnique)->count();

    return view('dashboard.customers', [
      'totalUser' => $userCount,
      'verified' => $verified,
      'notVerified' => $notVerified,
      'userDuplicates' => $userDuplicates,
    ]);
  }

  public function containers()
  {
    return view('dashboard.containers');
  }

  public function warehouses()
  {
    return view('dashboard.warehouses');
  }

  public function sizeAndCategory()
  {
    return view('dashboard.size_and_category');
  }
  /**
   * Display a listing of the resource.
   *
   * @return \Illuminate\Http\Response
   */
  public function indexCustomers(Request $request)
  {
    $columns = [
      1 => 'id',
      2 => 'billing_address',
      3 => 'billing_city',
      4 => 'billing_state',
      5 => 'billing_zip',
      6 => 'billing_country',
      7 => 'shipping_address',
      8 => 'shipping_city',
      9 => 'shipping_state',
      10 => 'shipping_zip',
      11 => 'shipping_country',
      12 => 'container_id',
      13 => 'subtotal',
      14 => 'delivery_cost',
      15 => 'tax_amount',
      16 => 'total',
      17 => 'stripe_transaction_id',
    ];

    $search = [];

    $totalData = Customer::count();
    $totalFiltered = $totalData;

    $limit = $request->input('length');
    $start = $request->input('start');
    $order = $columns[$request->input('order.0.column')];
    $dir = $request->input('order.0.dir');

    if (empty($request->input('search.value'))) {
      $customers = Customer::with('container')
        ->offset($start)
        ->limit($limit)
        ->orderBy($order, $dir)
        ->get();
    } else {
      $search = $request->input('search.value');
      $customers = Customer::with('container')
        ->where('id', 'LIKE', "%{$search}%")
        ->orWhere('billing_address', 'LIKE', "%{$search}%")
        ->orWhere('billing_city', 'LIKE', "%{$search}%")
        ->orWhere('billing_state', 'LIKE', "%{$search}%")
        ->orWhere('billing_zip', 'LIKE', "%{$search}%")
        ->orWhere('billing_country', 'LIKE', "%{$search}%")
        ->orWhere('shipping_address', 'LIKE', "%{$search}%")
        ->orWhere('shipping_city', 'LIKE', "%{$search}%")
        ->orWhere('shipping_state', 'LIKE', "%{$search}%")
        ->orWhere('shipping_zip', 'LIKE', "%{$search}%")
        ->orWhere('shipping_country', 'LIKE', "%{$search}%")
        ->orWhere('subtotal', 'LIKE', "%{$search}%")
        ->orWhere('delivery_cost', 'LIKE', "%{$search}%")
        ->orWhere('tax_amount', 'LIKE', "%{$search}%")
        ->orWhere('total', 'LIKE', "%{$search}%")
        ->orWhere('stripe_transaction_id', 'LIKE', "%{$search}%")
        ->offset($start)
        ->limit($limit)
        ->orderBy($order, $dir)
        ->get();
      $totalFiltered = Customer::where('id', 'LIKE', "%{$search}%")
        ->orWhere('billing_address', 'LIKE', "%{$search}%")
        ->orWhere('billing_city', 'LIKE', "%{$search}%")
        ->orWhere('billing_state', 'LIKE', "%{$search}%")
        ->orWhere('billing_zip', 'LIKE', "%{$search}%")
        ->orWhere('billing_country', 'LIKE', "%{$search}%")
        ->orWhere('shipping_address', 'LIKE', "%{$search}%")
        ->orWhere('shipping_city', 'LIKE', "%{$search}%")
        ->orWhere('shipping_state', 'LIKE', "%{$search}%")
        ->orWhere('shipping_zip', 'LIKE', "%{$search}%")
        ->orWhere('shipping_country', 'LIKE', "%{$search}%")
        ->orWhere('subtotal', 'LIKE', "%{$search}%")
        ->orWhere('delivery_cost', 'LIKE', "%{$search}%")
        ->orWhere('tax_amount', 'LIKE', "%{$search}%")
        ->orWhere('total', 'LIKE', "%{$search}%")
        ->orWhere('stripe_transaction_id', 'LIKE', "%{$search}%")
        ->count();
    }

    $data = [];
    if (!empty($customers)) {
      $ids = $start;
      foreach ($customers as $customer) {
        $nestedData['id'] = $customer->id;
        $nestedData['fake_id'] = ++$ids;
        $nestedData['billing_address'] = $customer->billing_address;
        $nestedData['billing_city'] = $customer->billing_city;
        $nestedData['billing_state'] = $customer->billing_state;
        $nestedData['billing_zip'] = $customer->billing_zip;
        $nestedData['billing_country'] = $customer->billing_country;
        $nestedData['shipping_address'] = $customer->shipping_address;
        $nestedData['shipping_city'] = $customer->shipping_city;
        $nestedData['shipping_state'] = $customer->shipping_state;
        $nestedData['shipping_zip'] = $customer->shipping_zip;
        $nestedData['shipping_country'] = $customer->shipping_country;
        $nestedData['container'] = $customer->container ? $customer->container->title : '';
        $nestedData['container_id'] = $customer->container_id;
        $nestedData['subtotal'] = $customer->subtotal;
        $nestedData['delivery_cost'] = $customer->delivery_cost;
        $nestedData['tax_amount'] = $customer->tax_amount;
        $nestedData['total'] = $customer->total;
        $nestedData['stripe_transaction_id'] = $customer->stripe_transaction_id;
        $data[] = $nestedData;
      }
    }

    if ($data) {
      return response()->json([
        'draw' => intval($request->input('draw')),
        'recordsTotal' => intval($totalData),
        'recordsFiltered' => intval($totalFiltered),
        'code' => 200,
        'data' => $data,
      ]);
    } else {
      return response()->json([
        'message' => 'Internal Server Error',
        'code' => 500,
        'data' => [],
      ]);
    }
  }

  /**
   * Display a listing of the resource.
   *
   * @return \Illuminate\Http\Response
   */
  public function indexContainers(Request $request)
  {
    $columns = [
      1 => 'id',
      2 => 'title',
      3 => 'size_id',
      4 => 'category_id',
      5 => 'type',
      6 => 'rental_price',
      7 => 'purchase_price',
      8 => 'warehouse_id',
      9 => 'delivery_cost',
    ];

    $search = [];

    $totalData = Container::count();
    $totalFiltered = $totalData;

    $limit = $request->input('length');
    $start = $request->input('start');
    $order = $columns[$request->input('order.0.column')];
    $dir = $request->input('order.0.dir');

    if (empty($request->input('search.value'))) {
      $containers = Container::with(['size', 'category', 'warehouse'])
        ->offset($start)
        ->limit($limit)
        ->orderBy($order, $dir)
        ->get();
    } else {
      $search = $request->input('search.value');
      $containers = Container::with(['size', 'category', 'warehouse'])
        ->where('id', 'LIKE', "%{$search}%")
        ->orWhere('title', 'LIKE', "%{$search}%")
        ->orWhere('type', 'LIKE', "%{$search}%")
        ->orWhere('rental_price', 'LIKE', "%{$search}%")
        ->orWhere('purchase_price', 'LIKE', "%{$search}%")
        ->orWhere('delivery_cost', 'LIKE', "%{$search}%")
        ->offset($start)
        ->limit($limit)
        ->orderBy($order, $dir)
        ->get();
      $totalFiltered = Container::where('id', 'LIKE', "%{$search}%")
        ->orWhere('title', 'LIKE', "%{$search}%")
        ->orWhere('type', 'LIKE', "%{$search}%")
        ->orWhere('rental_price', 'LIKE', "%{$search}%")
        ->orWhere('purchase_price', 'LIKE', "%{$search}%")
        ->orWhere('delivery_cost', 'LIKE', "%{$search}%")
        ->count();
    }

    $data = [];
    if (!empty($containers)) {
      $ids = $start;
      foreach ($containers as $container) {
        $nestedData['id'] = $container->id;
        $nestedData['fake_id'] = ++$ids;
        $nestedData['title'] = $container->title;
        $nestedData['size'] = $container->size ? $container->size->size : '';
        $nestedData['category'] = $container->category ? $container->category->title : '';
        $nestedData['type'] = $container->type;
        $nestedData['rental_price'] = $container->rental_price;
        $nestedData['purchase_price'] = $container->purchase_price;
        $nestedData['delivery_cost'] = $container->delivery_cost;
        $nestedData['warehouse'] = $container->warehouse ? $container->warehouse->location : '';
        $data[] = $nestedData;
      }
    }

    if ($data) {
      return response()->json([
        'draw' => intval($request->input('draw')),
        'recordsTotal' => intval($totalData),
        'recordsFiltered' => intval($totalFiltered),
        'code' => 200,
        'data' => $data,
      ]);
    } else {
      return response()->json([
        'message' => 'Internal Server Error',
        'code' => 500,
        'data' => [],
      ]);
    }
  }

  /**
   * Display a listing of the resource.
   *
   * @return \Illuminate\Http\Response
   */
  public function indexWarehouses(Request $request)
  {
    $columns = [
      1 => 'id',
      2 => 'location',
      3 => 'latitude',
      4 => 'longitude',
    ];

    $search = [];

    $totalData = Warehouse::count();
    $totalFiltered = $totalData;

    $limit = $request->input('length');
    $start = $request->input('start');
    $order = $columns[$request->input('order.0.column')];
    $dir = $request->input('order.0.dir');

    if (empty($request->input('search.value'))) {
      $warehouses = Warehouse::offset($start)
        ->limit($limit)
        ->orderBy($order, $dir)
        ->get();
    } else {
      $search = $request->input('search.value');
      $warehouses = Warehouse::where('id', 'LIKE', "%{$search}%")
        ->orWhere('location', 'LIKE', "%{$search}%")
        ->orWhere('latitude', 'LIKE', "%{$search}%")
        ->orWhere('longitude', 'LIKE', "%{$search}%")
        ->offset($start)
        ->limit($limit)
        ->orderBy($order, $dir)
        ->get();
      $totalFiltered = Warehouse::where('id', 'LIKE', "%{$search}%")
        ->orWhere('location', 'LIKE', "%{$search}%")
        ->orWhere('latitude', 'LIKE', "%{$search}%")
        ->orWhere('longitude', 'LIKE', "%{$search}%")
        ->count();
    }

    $data = [];
    if (!empty($warehouses)) {
      $ids = $start;
      foreach ($warehouses as $warehouse) {
        $nestedData['id'] = $warehouse->id;
        $nestedData['fake_id'] = ++$ids;
        $nestedData['location'] = $warehouse->location;
        $nestedData['latitude'] = $warehouse->latitude;
        $nestedData['longitude'] = $warehouse->longitude;
        $data[] = $nestedData;
      }
    }

    if ($data) {
      return response()->json([
        'draw' => intval($request->input('draw')),
        'recordsTotal' => intval($totalData),
        'recordsFiltered' => intval($totalFiltered),
        'code' => 200,
        'data' => $data,
      ]);
    } else {
      return response()->json([
        'message' => 'Internal Server Error',
        'code' => 500,
        'data' => [],
      ]);
    }
  }

  /**
   * Display a listing of the resource.
   *
   * @return \Illuminate\Http\Response
   */
  public function indexSize(Request $request)
  {
    $columns = [
      1 => 'id',
      2 => 'size',
      3 => 'cost_per_km',
    ];

    $search = [];

    $totalData = Size::count();

    $totalFiltered = $totalData;

    $limit = $request->input('length');
    $start = $request->input('start');
    $order = $columns[$request->input('order.0.column')];
    $dir = $request->input('order.0.dir');

    if (empty($request->input('search.value'))) {
      $sizes = Size::offset($start)
        ->limit($limit)
        ->orderBy($order, $dir)
        ->get();
    } else {
      $search = $request->input('search.value');

      $sizes = Size::where('id', 'LIKE', "%{$search}%")
        ->orWhere('size', 'LIKE', "%{$search}%")
        ->orWhere('cost_per_km', 'LIKE', "%{$search}%")
        ->offset($start)
        ->limit($limit)
        ->orderBy($order, $dir)
        ->get();

      $totalFiltered = Size::where('id', 'LIKE', "%{$search}%")
        ->orWhere('size', 'LIKE', "%{$search}%")
        ->orWhere('cost_per_km', 'LIKE', "%{$search}%")
        ->count();
    }

    $data = [];

    if (!empty($sizes)) {
      // providing a dummy id instead of database ids
      $ids = $start;

      foreach ($sizes as $size) {
        $nestedData['id'] = $size->id;
        $nestedData['fake_id'] = ++$ids;
        $nestedData['size'] = $size->size;
        $nestedData['cost_per_km'] = $size->cost_per_km;

        $data[] = $nestedData;
      }
    }

    if ($data) {
      return response()->json([
        'draw' => intval($request->input('draw')),
        'recordsTotal' => intval($totalData),
        'recordsFiltered' => intval($totalFiltered),
        'code' => 200,
        'data' => $data,
      ]);
    } else {
      return response()->json([
        'message' => 'Internal Server Error',
        'code' => 500,
        'data' => [],
      ]);
    }
  }

  /**
   * Display a listing of the resource.
   *
   * @return \Illuminate\Http\Response
   */
  public function indexCategory(Request $request)
  {
    $columns = [
      1 => 'id',
      2 => 'title',
    ];

    $search = [];

    $totalData = Category::count();
    $totalFiltered = $totalData;

    $limit = $request->input('length');
    $start = $request->input('start');
    $order = $columns[$request->input('order.0.column')];
    $dir = $request->input('order.0.dir');

    if (empty($request->input('search.value'))) {
      $categories = Category::offset($start)
        ->limit($limit)
        ->orderBy($order, $dir)
        ->get();
    } else {
      $search = $request->input('search.value');
      $categories = Category::where('id', 'LIKE', "%{$search}%")
        ->orWhere('title', 'LIKE', "%{$search}%")
        ->offset($start)
        ->limit($limit)
        ->orderBy($order, $dir)
        ->get();
      $totalFiltered = Category::where('id', 'LIKE', "%{$search}%")
        ->orWhere('title', 'LIKE', "%{$search}%")
        ->count();
    }

    $data = [];
    if (!empty($categories)) {
      $ids = $start;
      foreach ($categories as $category) {
        $nestedData['id'] = $category->id;
        $nestedData['fake_id'] = ++$ids;
        $nestedData['title'] = $category->title;
        $data[] = $nestedData;
      }
    }

    if ($data) {
      return response()->json([
        'draw' => intval($request->input('draw')),
        'recordsTotal' => intval($totalData),
        'recordsFiltered' => intval($totalFiltered),
        'code' => 200,
        'data' => $data,
      ]);
    } else {
      return response()->json([
        'message' => 'Internal Server Error',
        'code' => 500,
        'data' => [],
      ]);
    }
  }

  /**
   * Store a newly created resource in storage.
   *
   * @param  \Illuminate\Http\Request  $request
   * @return \Illuminate\Http\Response
   */
  public function storeCustomers(Request $request)
  {
    $customerID = $request->id;
    $customerData = [
      'billing_address' => $request->billing_address,
      'billing_city' => $request->billing_city,
      'billing_state' => $request->billing_state,
      'billing_zip' => $request->billing_zip,
      'billing_country' => $request->billing_country,
      'shipping_address' => $request->shipping_address,
      'shipping_city' => $request->shipping_city,
      'shipping_state' => $request->shipping_state,
      'shipping_zip' => $request->shipping_zip,
      'shipping_country' => $request->shipping_country,
      'container_id' => $request->container_id,
      'subtotal' => $request->subtotal,
      'delivery_cost' => $request->delivery_cost,
      'tax_amount' => $request->tax_amount,
      'total' => $request->total,
      'stripe_transaction_id' => $request->stripe_transaction_id,
    ];
    if ($customerID) {
      $customer = Customer::updateOrCreate(
        ['id' => $customerID],
        $customerData
      );
      return response()->json('Updated');
    } else {
      $customer = Customer::updateOrCreate(
        ['id' => $customerID],
        $customerData
      );
      return response()->json('Created');
    }
  }

  /**
   * Store a newly created resource in storage.
   *
   * @param  \Illuminate\Http\Request  $request
   * @return \Illuminate\Http\Response
   */
  public function storeContainers(Request $request)
  {
    $containerID = $request->id;
    $data = [
        'title' => $request->title,
        'description' => $request->description,
        'size_id' => $request->size_id,
        'category_id' => $request->category_id,
        'type' => $request->type,
        'rental_price' => $request->rental_price,
        'purchase_price' => $request->purchase_price,
        'warehouse_id' => $request->warehouse_id,
        'delivery_cost' => $request->delivery_cost,
    ];
    if ($request->hasFile('image')) {
        $image = $request->file('image');
        $path = $image->store('containers', 'public');
        $data['image'] = '/storage/' . $path;
    } elseif ($containerID) {
        $existing = Container::find($containerID);
        if ($existing) {
            $data['image'] = $existing->image;
        }
    }
    if ($containerID) {
        $container = Container::updateOrCreate(
            ['id' => $containerID],
            $data
        );
        return response()->json('Updated');
    } else {
        $containerTitle = Container::where('title', $request->title)->first();
        if (empty($containerTitle)) {
            $container = Container::updateOrCreate(
                ['id' => $containerID],
                $data
            );
            return response()->json('Created');
        } else {
            return response()->json(['message' => "already exists"], 422);
        }
    }
  }

  /**
   * Store a newly created resource in storage.
   *
   * @param  \Illuminate\Http\Request  $request
   * @return \Illuminate\Http\Response
   */
  public function storeWarehouses(Request $request)
  {
    $warehouseID = $request->id;
    if ($warehouseID) {
      // update the value
      $warehouse = Warehouse::updateOrCreate(
        ['id' => $warehouseID],
        [
          'location' => $request->location,
          'latitude' => $request->latitude,
          'longitude' => $request->longitude
        ]
      );
      return response()->json('Updated');
    } else {
      // create new one if location is unique
      $warehouseLocation = Warehouse::where('location', $request->location)->first();
      if (empty($warehouseLocation)) {
        $warehouse = Warehouse::updateOrCreate(
          ['id' => $warehouseID],
          [
            'location' => $request->location,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude
          ]
        );
        return response()->json('Created');
      } else {
        // warehouse already exists
        return response()->json(['message' => "already exists"], 422);
      }
    }
  }

  /**
   * Store a newly created resource in storage.
   *
   * @param  \Illuminate\Http\Request  $request
   * @return \Illuminate\Http\Response
   */
  public function storeSize(Request $request)
  {
    $sizeID = $request->id;

    if ($sizeID) {
      // update the value
      $sizes = Size::updateOrCreate(
        ['id' => $sizeID],
        ['size' => $request->size, 'cost_per_km' => $request->cost_per_km]
      );

      // user updated
      return response()->json('Updated');
    } else {
      // create new one if email is unique
      $size = Size::where('size', $request->size)->first();

      if (empty($size)) {
        $sizes = Size::updateOrCreate(
          ['id' => $sizeID],
          ['size' => $request->size, 'cost_per_km' => $request->cost_per_km]
        );

        // user created
        return response()->json('Created');
      } else {
        // user already exist
        return response()->json(['message' => "already exits"], 422);
      }
    }
  }

  public function storeCategory(Request $request)
  {
    $categoryID = $request->id;
    if ($categoryID) {
      // update the value
      $category = Category::updateOrCreate(
        ['id' => $categoryID],
        ['title' => $request->title]
      );
      return response()->json('Updated');
    } else {
      // create new one if title is unique
      $categoryTitle = Category::where('title', $request->title)->first();
      if (empty($categoryTitle)) {
        $category = Category::updateOrCreate(
          ['id' => $categoryID],
          ['title' => $request->title]
        );
        return response()->json('Created');
      } else {
        // category already exists
        return response()->json(['message' => "already exists"], 422);
      }
    }
  }

  /**
   * Show the form for editing the specified resource.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function editCustomers($id): JsonResponse
  {
    $customer = Customer::findOrFail($id);
    return response()->json($customer);
  }

  /**
   * Show the form for editing the specified resource.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function editContainers($id): JsonResponse
  {
    $container = Container::findOrFail($id);
    return response()->json($container);
  }

  /**
   * Show the form for editing the specified resource.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function editWarehouses($id): JsonResponse
  {
    $warehouse = Warehouse::findOrFail($id);
    return response()->json($warehouse);
  }

  /**
   * Show the form for editing the specified resource.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function editSize($id): JsonResponse
  {
    $size = Size::findOrFail($id);
    return response()->json($size);
  }

  /**
   * Show the form for editing the specified resource.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function editCategory($id): JsonResponse
  {
    $category = Category::findOrFail($id);
    return response()->json($category);
  }

  /**
   * Remove the specified resource from storage.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function destroyCustomers($id)
  {
    $users = User::where('id', $id)->delete();
  }

  /**
   * Remove the specified resource from storage.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function destroyContainers($id)
  {
    $container = Container::where('id', $id)->delete();
  }

  /**
   * Remove the specified resource from storage.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function destroyWarehouses($id)
  {
    $warehouse = Warehouse::where('id', $id)->delete();
  }

  /**
   * Remove the specified resource from storage.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function destroySize($id)
  {
    $sizes = Size::where('id', $id)->delete();
  }

  /**
   * Remove the specified resource from storage.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function destroyCategory($id)
  {
    $category = Category::where('id', $id)->delete();
  }
}
