<?php $__env->startSection('title', 'Containers'); ?>

<!-- Vendor Styles -->
<?php $__env->startSection('vendor-style'); ?>
<?php echo app('Illuminate\Foundation\Vite')([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
]); ?>
<?php $__env->stopSection(); ?>

<!-- Vendor <PERSON> -->
<?php $__env->startSection('vendor-script'); ?>
<?php echo app('Illuminate\Foundation\Vite')([
  'resources/assets/vendor/libs/moment/moment.js',
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
]); ?>
<?php $__env->stopSection(); ?>

<!-- Page Scripts -->
<?php $__env->startSection('page-script'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/containers.js']); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Containers List Table -->
<div class="card">
  <div class="card-header border-bottom">
    <h5 class="card-title mb-0">Containers</h5>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-containers table">
      <thead class="border-top">
        <tr>
          <th></th>
          <th>Id</th>
          <th>Title</th>
          <th>Size</th>
          <th>Category</th>
          <th>Type</th>
          <th>Rental Price</th>
          <th>Purchase Price</th>
          <th>Delivery Cost</th>
          <th>Warehouse</th>
          <th>Actions</th>
        </tr>
      </thead>
    </table>
  </div>
  
  <!-- Offcanvas to add new container -->
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddContainer" aria-labelledby="offcanvasAddContainerLabel">
    <div class="offcanvas-header border-bottom">
      <h5 id="offcanvasAddContainerLabel" class="offcanvas-title">Add Container</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body mx-0 flex-grow-0 p-6 h-100">
      <form class="add-new-container pt-0" id="addNewContainerForm" enctype="multipart/form-data">
        <input type="hidden" name="id" id="container_id">
        
        <div class="mb-6">
          <label class="form-label" for="add-container-title">Title</label>
          <input type="text" class="form-control" id="add-container-title" placeholder="Container Title" name="title" aria-label="Container Title" />
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-description">Description</label>
          <textarea class="form-control" id="add-container-description" placeholder="Description" name="description" aria-label="Description"></textarea>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-size">Size</label>
          <select class="form-select" id="add-container-size" name="size_id">
            <option value="">Select Size</option>
          </select>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-category">Category</label>
          <select class="form-select" id="add-container-category" name="category_id">
            <option value="">Select Category</option>
          </select>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-type">Type</label>
          <select class="form-select" id="add-container-type" name="type">
            <option value="">Select Type</option>
            <option value="rental">Rental</option>
            <option value="purchase">Purchase</option>
            <option value="both">Both</option>
          </select>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-rental-price">Rental Price</label>
          <input type="number" step="0.01" class="form-control" id="add-container-rental-price" placeholder="Rental Price" name="rental_price" aria-label="Rental Price" />
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-purchase-price">Purchase Price</label>
          <input type="number" step="0.01" class="form-control" id="add-container-purchase-price" placeholder="Purchase Price" name="purchase_price" aria-label="Purchase Price" />
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-delivery-cost">Delivery Cost</label>
          <input type="number" step="0.01" class="form-control" id="add-container-delivery-cost" placeholder="Delivery Cost" name="delivery_cost" aria-label="Delivery Cost" />
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-warehouse">Warehouse</label>
          <select class="form-select" id="add-container-warehouse" name="warehouse_id">
            <option value="">Select Warehouse</option>
          </select>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-image">Image</label>
          <input type="file" class="form-control" id="add-container-image" name="image" accept="image/*" />
          <div id="container-image-preview" class="mt-2"></div>
        </div>
        
        <button type="submit" class="btn btn-primary me-3 data-submit">Submit</button>
        <button type="reset" class="btn btn-label-danger" data-bs-dismiss="offcanvas">Cancel</button>
      </form>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts/layoutMaster', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\containers\resources\views/dashboard/containers.blade.php ENDPATH**/ ?>