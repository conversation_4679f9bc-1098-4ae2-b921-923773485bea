<?php $__env->startSection('title', 'Containers'); ?>

<!-- Vendor Styles -->
<?php $__env->startSection('vendor-style'); ?>
<?php echo app('Illuminate\Foundation\Vite')([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
]); ?>
<?php $__env->stopSection(); ?>

<!-- Vendor Scripts -->
<?php $__env->startSection('vendor-script'); ?>
<?php echo app('Illuminate\Foundation\Vite')([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
]); ?>
<?php $__env->stopSection(); ?>

<!-- Page Scripts -->
<?php $__env->startSection('page-script'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/containers.js']); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Containers List Table -->
<div class="card">
  <div class="card-header border-bottom">
    <h5 class="card-title mb-0">Containers</h5>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-containers table">
      <thead class="border-top">
        <tr>
          <th></th>
          <th>Id</th>
          <th>Title</th>
          <th>Size</th>
          <th>Category</th>
          <th>Type</th>
          <th>Rental Price</th>
          <th>Purchase Price</th>
          <th>Delivery Cost</th>
          <th>Warehouse</th>
          <th>Actions</th>
        </tr>
      </thead>
    </table>
  </div>
  
  <!-- Offcanvas to add new container -->
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddContainer" aria-labelledby="offcanvasAddContainerLabel">
    <div class="offcanvas-header border-bottom">
      <h5 id="offcanvasAddContainerLabel" class="offcanvas-title">Add Container</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body mx-0 flex-grow-0 p-6 h-100">
      <form class="needs-validation" id="addNewContainerForm" enctype="multipart/form-data" novalidate>
        <input type="hidden" name="id" id="container_id">
        
        <div class="mb-6">
          <label class="form-label" for="add-container-title">Title *</label>
          <input type="text" class="form-control" id="add-container-title" 
                 placeholder="Container Title" name="title" required>
          <div class="invalid-feedback">
            Please enter a container title.
          </div>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-description">Description</label>
          <textarea class="form-control" id="add-container-description" 
                    placeholder="Description" name="description"></textarea>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-size">Size *</label>
          <select class="form-select" id="add-container-size" name="size_id" required>
            <option value="">Select Size</option>
          </select>
          <div class="invalid-feedback">
            Please select a size.
          </div>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-category">Category *</label>
          <select class="form-select" id="add-container-category" name="category_id" required>
            <option value="">Select Category</option>
          </select>
          <div class="invalid-feedback">
            Please select a category.
          </div>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-type">Type *</label>
          <select class="form-select" id="add-container-type" name="type" required>
            <option value="">Select Type</option>
            <option value="rental">Rental</option>
            <option value="purchase">Purchase</option>
            <option value="both">Both</option>
          </select>
          <div class="invalid-feedback">
            Please select a type.
          </div>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-rental-price">Rental Price *</label>
          <input type="number" step="0.01" class="form-control" id="add-container-rental-price" 
                 placeholder="0.00" name="rental_price" required>
          <div class="invalid-feedback">
            Please enter a valid rental price.
          </div>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-purchase-price">Purchase Price *</label>
          <input type="number" step="0.01" class="form-control" id="add-container-purchase-price" 
                 placeholder="0.00" name="purchase_price" required>
          <div class="invalid-feedback">
            Please enter a valid purchase price.
          </div>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-delivery-cost">Delivery Cost *</label>
          <input type="number" step="0.01" class="form-control" id="add-container-delivery-cost" 
                 placeholder="0.00" name="delivery_cost" required>
          <div class="invalid-feedback">
            Please enter a valid delivery cost.
          </div>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-warehouse">Warehouse *</label>
          <select class="form-select" id="add-container-warehouse" name="warehouse_id" required>
            <option value="">Select Warehouse</option>
          </select>
          <div class="invalid-feedback">
            Please select a warehouse.
          </div>
        </div>
        
        <div class="mb-6">
          <label class="form-label" for="add-container-image">Image</label>
          <input type="file" class="form-control" id="add-container-image" name="image" accept="image/*">
          <div id="container-image-preview" class="mt-2"></div>
        </div>
        
        <button type="submit" class="btn btn-primary me-3 data-submit">Submit</button>
        <button type="reset" class="btn btn-label-danger" data-bs-dismiss="offcanvas">Cancel</button>
      </form>
    </div>
  </div>
</div>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts/layoutMaster', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\containers\resources\views/dashboard/containers.blade.php ENDPATH**/ ?>