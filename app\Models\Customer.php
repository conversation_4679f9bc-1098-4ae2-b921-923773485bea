<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    protected $fillable = [
        'billing_address',
        'billing_city',
        'billing_state',
        'billing_zip',
        'billing_country',
        'shipping_address',
        'shipping_city',
        'shipping_state',
        'shipping_zip',
        'shipping_country',
        'container_id',
        'subtotal',
        'delivery_cost',
        'tax_amount',
        'total',
        'stripe_transaction_id',
    ];

    public function container()
    {
        return $this->belongsTo(Container::class);
    }
}
