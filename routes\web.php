<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\language\LanguageController;
use App\Http\Controllers\pages\HomePage;
use App\Http\Controllers\pages\Page2;
use App\Http\Controllers\pages\MiscError;
use App\Http\Controllers\authentications\LoginBasic;
use App\Http\Controllers\authentications\RegisterBasic;
use App\Http\Controllers\dashboard\DashboardController;
use App\Models\Size;
use App\Models\Category;
use App\Models\Warehouse;
use App\Livewire\ContainerCatalog;
use App\Livewire\Checkout;
use App\Livewire\CheckoutPayment;
use App\Livewire\CartItems;
use App\Livewire\CheckoutForm;

// Main Page Route

Route::get('/', function () {
    return view('front.catalog');
})->name('catalog');

// Cart Route
Route::get('/cart', function () {
    return view('front.cart');
})->name('cart');

// Checkout Route
Route::get('/checkout', function () {
    return view('front.checkout');
})->name('checkout');

Route::get('/checkout/payment', function () {
    return view('front.checkout-payment');
})->name('checkout.payment');

Route::get('/checkout/success', function () {
    return view('front.checkout-success');
})->name('checkout.success');

Route::middleware([
  'auth:sanctum',
  config('jetstream.auth_session'),
  'verified',
])->group(function () {
  // Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
  Route::get('/dashboard/orders', [DashboardController::class, 'customers'])->name('dashboard-orders');
  Route::get('/dashboard/containers', [DashboardController::class, 'containers'])->name('dashboard-containers');
  Route::get('/dashboard/warehouses', [DashboardController::class, 'warehouses'])->name('dashboard-warehouses');
  Route::get('/dashboard/size-and-category', [DashboardController::class, 'sizeAndCategory'])->name('dashboard-size-and-category');

  Route::get('/size', function () {
    return Size::all(['id', 'size']);
  });

  Route::get('/category', function () {
    return Category::all(['id', 'title']);
  });

  Route::get('/warehouses', function () {
    return Warehouse::all(['id', 'location']);
  });

  Route::get('/dashboard/size-list', [DashboardController::class, 'indexSize'])->name('dashboard-size-list');
  Route::get('/dashboard/size-list/{id}/edit', [DashboardController::class, 'editSize'])->name('dashboard-size-edit');
  Route::post('/dashboard/size-list', [DashboardController::class, 'storeSize'])->name('dashboard-size-store');
  Route::delete('/dashboard/size-list/{id}', [DashboardController::class, 'destroySize'])->name('dashboard-size-destroy');

  // Category routes
  Route::get('/dashboard/category-list', [DashboardController::class, 'indexCategory'])->name('dashboard-category-list');
  Route::get('/dashboard/category-list/{id}/edit', [DashboardController::class, 'editCategory'])->name('dashboard-category-edit');
  Route::post('/dashboard/category-list', [DashboardController::class, 'storeCategory'])->name('dashboard-category-store');
  Route::delete('/dashboard/category-list/{id}', [DashboardController::class, 'destroyCategory'])->name('dashboard-category-destroy');

  Route::get('/dashboard/warehouses-list', [DashboardController::class, 'indexWarehouses'])->name('dashboard-warehouses-list');
  Route::get('/dashboard/warehouses-list/{id}/edit', [DashboardController::class, 'editWarehouses'])->name('dashboard-warehouses-edit');
  Route::post('/dashboard/warehouses-list', [DashboardController::class, 'storeWarehouses'])->name('dashboard-warehouses-store');
  Route::delete('/dashboard/warehouses-list/{id}', [DashboardController::class, 'destroyWarehouses'])->name('dashboard-warehouses-destroy');

  Route::get('/dashboard/containers-list', [DashboardController::class, 'indexContainers'])->name('dashboard-containers-list');
  Route::get('/dashboard/containers-list/{id}/edit', [DashboardController::class, 'editContainers'])->name('dashboard-containers-edit');
  Route::post('/dashboard/containers-list', [DashboardController::class, 'storeContainers'])->name('dashboard-containers-store');
  Route::delete('/dashboard/containers-list/{id}', [DashboardController::class, 'destroyContainers'])->name('dashboard-containers-destroy');

  // Debug route to check containers data
  Route::get('/debug/containers', function() {
    $containers = \App\Models\Container::with(['size', 'category', 'warehouse'])->get();
    return response()->json([
      'total_containers' => $containers->count(),
      'containers' => $containers->toArray()
    ]);
  });

  Route::get('/dashboard/customers-list', [DashboardController::class, 'indexCustomers'])->name('dashboard-customers-list');
  Route::get('/dashboard/customers-list/{id}/edit', [DashboardController::class, 'editCustomers'])->name('dashboard-customers-edit');
  Route::post('/dashboard/customers-list', [DashboardController::class, 'storeCustomers'])->name('dashboard-customers-store');
  Route::delete('/dashboard/customers-list/{id}', [DashboardController::class, 'destroyCustomers'])->name('dashboard-customers-destroy');
});
