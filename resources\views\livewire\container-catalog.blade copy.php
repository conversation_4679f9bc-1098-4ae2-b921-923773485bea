<div>
    <!-- Header -->
    <header class="bg-white border-bottom py-3">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-boxes fa-2x text-primary me-2"></i>
                        <div>
                            <h4 class="mb-0 fw-bold">Storage-Tech</h4>
                            <small class="text-muted">Premium Storage Solutions</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <h6 class="mb-0 text-muted">Professional Container Rentals & Sales</h6>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex align-items-center justify-content-end">

                        <div class="cart-badge" style="cursor:pointer;" data-bs-toggle="offcanvas" data-bs-target="#cartOffcanvas" aria-controls="cartOffcanvas">
                            <i class="fas fa-shopping-cart fa-lg text-primary"></i>
                            <span class="cart-count">{{ count($cart) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <!-- Filter Sidebar -->
            <div class="col-md-3 p-4">
                <div class="sticky-top" style="top: 20px;">
                    <button class="btn btn-outline-secondary w-30 mb-3">
                        <i class="fas fa-filter me-2"></i>Filters
                    </button>

                    <h5 class="mb-3">Filter Containers</h5>

                    <!-- Size Filter -->
                    <div class="filter-section">
                        <h6 class="mb-2">Size</h6>
                        @foreach($sizes as $size)
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="{{ $size }}"
                                       wire:model.live="filterSize" id="size-{{ $size }}">
                                <label class="form-check-label" for="size-{{ $size }}">
                                    {{ $size }}ft
                                </label>
                            </div>
                        @endforeach
                    </div>

                    <!-- Category Filter -->
                    <div class="filter-section">
                        <h6 class="mb-2">Category</h6>
                        @foreach($categories as $category)
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="{{ $category->id }}"
                                       wire:model.live="filterCategory" id="cat-{{ $category->id }}">
                                <label class="form-check-label" for="cat-{{ $category->id }}">
                                    {{ $category->title }}
                                </label>
                            </div>
                        @endforeach
                    </div>

                    <!-- Type Filter -->
                    <div class="filter-section">
                        <h6 class="mb-2">Type</h6>
                        @foreach(['rental', 'purchase'] as $type)
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="{{ $type }}"
                                       wire:model.live="filterType" id="type-{{ $type }}">
                                <label class="form-check-label" for="type-{{ $type }}">
                                    {{ ucfirst($type) }}
                                </label>
                            </div>
                        @endforeach
                        {{-- <small class="text-muted">"Both" type containers will be shown regardless of selection</small> --}}
                    </div>

                    <!-- Price Range -->
                    <div class="filter-section">
                        <h6 class="mb-2">Price Range</h6>
                        <input type="range" class="form-range price-slider" min="0" max="100000"
                               wire:model.live="priceRange">
                        <div class="d-flex justify-content-between">
                            <small>$0</small>
                            <small>$100,000</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-md-6 p-4">
                                <!-- Search and Sort Bar -->
                <div class="mb-4">
                    <div class="row">
                        <div class="col-md-8">
                            <input type="text" class="form-control" placeholder="Search containers..."
                                   wire:model.live="filterSearch">
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" wire:model.live="filterSort">
                                <option value="">Sort by</option>
                                <option value="price-asc">Price: Low to High</option>
                                <option value="price-desc">Price: High to Low</option>
                                <option value="size">Size</option>
                            </select>
                        </div>
                    </div>
                </div>

                <h4 class="mb-4">{{ count($containers) }} Containers Available</h4>

                <div class="row">
                    @forelse($containers as $container)
                        <div class="col-lg-4 col-md-4 mb-4">
                            <div class="card container-card h-100">
                                <div class="position-relative">
                                    @if($container->image)
                                        <img src="{{ $container->image }}" class="card-img-top" alt="{{ $container->title }}" style="height: 200px; object-fit: cover;">
                                    @else
                                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                            <i class="fas fa-box fa-3x text-muted"></i>
                                        </div>
                                    @endif
                                    <div class="category-tag">{{ $container->category->title ?? 'General' }}</div>
                                </div>

                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title">{{ $container->title }}</h5>
                                    <p class="card-text text-muted">{{ $container->description }}</p>

                                    <div class="mb-3">
                                        <div class="spec-icon">
                                            <i class="fas fa-ruler-combined"></i>
                                            {{ $container->size->size ?? 'N/A' }}ft
                                        </div>
                                        <div class="spec-icon">
                                            <i class="fas fa-map-marker-alt"></i>
                                            {{ $container->warehouse->location ?? 'N/A' }}
                                        </div>
                                    </div>

                                    <!-- Type Dropdown -->
                                    <div class="mb-2">
                                        <label class="form-label">Type</label>
                                        <select class="form-select form-select-sm" wire:model="selectedType.{{ $container->id }}">
                                            @if($container->type === 'rental' || $container->type === 'both')
                                                <option value="Rental">Rental</option>
                                            @endif
                                            @if($container->type === 'purchase' || $container->type === 'both')
                                                <option value="Purchase">Purchase</option>
                                            @endif
                                        </select>
                                    </div>

                                    <!-- Duration Dropdown (only for rental) -->
                                    @php
                                        $currentSelectedType = $selectedType[$container->id] ?? ucfirst($container->type);
                                        $currentSelectedDuration = $selectedDuration[$container->id] ?? 1;
                                    @endphp

                                    @if($currentSelectedType === 'Rental')
                                        <div class="mb-2">
                                            <label class="form-label">Duration</label>
                                            <select class="form-select form-select-sm" wire:model="selectedDuration.{{ $container->id }}">
                                                <option value="1">1 month</option>
                                                <option value="2">2 months</option>
                                                <option value="3">3 months</option>
                                                <option value="4">4 months</option>
                                                <option value="5">5 months</option>
                                                <option value="6">6 months</option>
                                                <option value="7">7 months</option>
                                                <option value="8">8 months</option>
                                                <option value="9">9 months</option>
                                                <option value="10">10 months</option>
                                                <option value="11">11 months</option>
                                                <option value="12">12 months</option>
                                            </select>
                                        </div>
                                    @endif

                                    <div class="mt-auto">
                                        <div class="mb-3">
                                            @if($currentSelectedType === 'Rental')
                                                <span class="h5 text-primary">${{ number_format($container->rental_price * $currentSelectedDuration) }} for {{ $currentSelectedDuration }} month{{ $currentSelectedDuration > 1 ? 's' : '' }}</span>
                                            @else
                                                <span class="h5 text-primary">${{ number_format($container->purchase_price) }} to own</span>
                                            @endif
                                        </div>

                                        @if(($container->stock ?? 1) > 0)
                                            <button class="btn btn-primary w-100"
                                                    wire:click="addToCart({{ $container->id }}, '{{ $currentSelectedType }}', {{ $currentSelectedDuration }})">
                                                <i class="fas fa-plus me-2"></i>Add to Cart
                                            </button>
                                        @else
                                            <button class="btn btn-out-of-stock w-100" disabled>
                                                <i class="fas fa-times me-2"></i>Out of Stock
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-12 text-center py-5">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No containers found</h5>
                            <p class="text-muted">Try adjusting your filters to find what you're looking for.</p>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Cart Offcanvas (Side Modal) -->
            <div class="offcanvas offcanvas-end" tabindex="-1" id="cartOffcanvas" aria-labelledby="cartOffcanvasLabel" style="width: 400px;">
                <div class="offcanvas-header">
                    <h5 class="offcanvas-title" id="cartOffcanvasLabel">Shopping Cart</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                </div>
                <div class="offcanvas-body d-flex flex-column h-100">
                    @if(count($cart) > 0)
                        <p class="text-muted">{{ count($cart) }} items</p>

                        @foreach($cart as $key => $item)
                            <div class="d-flex align-items-start mb-3 pb-3 border-bottom">
                                <img src="{{ $item['image'] ?? 'https://via.placeholder.com/50' }}"
                                     class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $item['name'] }}</h6>
                                    <div class="small text-muted">
                                        {{ $item['type'] }}
                                        @if($item['type'] === 'Rental')
                                            ({{ $item['rentalMonths'] }} month{{ $item['rentalMonths'] > 1 ? 's' : '' }})
                                        @endif
                                    </div>
                                    <div class="small text-muted">{{ $item['size'] }}ft • {{ $item['warehouse'] }}</div>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold">${{ number_format($item['price']) }}</div>
                                    <button class="btn btn-link text-danger p-0"
                                            wire:click="removeFromCart('{{ $key }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        @endforeach

                        <div class="d-grid gap-2 mt-auto">
                            <button class="btn btn-outline-danger btn-sm" wire:click="clearCart">
                                Clear Cart
                            </button>
                            <button class="btn btn-primary" wire:click="checkout">
                                Proceed to Checkout
                            </button>
                        </div>
                    @else
                        <div class="text-center py-4 mt-auto">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Your cart is empty</h6>
                            <p class="text-muted small">Add some containers to get started!</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light border-top py-4 mt-5">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-boxes fa-lg text-primary me-2"></i>
                        <div>
                            <h6 class="mb-0 fw-bold">Storage-Tech</h6>
                            <small class="text-muted">Premium Storage Solutions</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        © {{ date('Y') }} Storage-Tech. All rights reserved. Professional container rentals and sales
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript for cart updates -->
    <script>
        document.addEventListener('livewire:load', function () {
            Livewire.on('cart-updated', function (data) {
                // Update cart count in header
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    cartCount.textContent = data.count;
                }
            });
        });
    </script>
</div>
