<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Customer;
use Illuminate\Support\Facades\Auth;

class CheckoutForm extends Component
{
    // Personal Information
    public $firstName = '';
    public $lastName = '';
    public $email = '';
    public $phone = '';

    // Delivery Address
    public $streetAddress = '';
    public $city = '';
    public $province = '';
    public $zipCode = '';



    // Order Data
    public $cart = [];
    public $subtotal = 0;
    public $deliveryCost = 0;
    public $tax = 0;
    public $total = 0;
    public $agreeToTerms = false;

    protected $rules = [
        'firstName' => 'required|string|max:255',
        'lastName' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'required|string|max:20',
        'streetAddress' => 'required|string|max:255',
        'city' => 'required|string|max:255',
        'province' => 'required|string|max:255',
        'zipCode' => 'required|string|max:10',
    ];

    public function mount()
    {
        $this->cart = session('cart', []);
        $this->subtotal = session('cart_subtotal', 0);
        $this->deliveryCost = session('delivery_cost', 0);
        $this->tax = session('cart_tax', 0);
        $this->total = $this->subtotal + $this->deliveryCost + $this->tax;
        // Load form data from session if available
        $this->firstName = session('checkout_form.firstName', '');
        $this->lastName = session('checkout_form.lastName', '');
        $this->email = session('checkout_form.email', '');
        $this->phone = session('checkout_form.phone', '');
        $this->streetAddress = session('checkout_form.streetAddress', '');
        $this->city = session('checkout_form.city', '');
        $this->province = session('checkout_form.province', '');
        $this->zipCode = session('checkout_form.zipCode', '');
        // Prefill form if user is logged in
        if (Auth::check()) {
            $user = Auth::user();
            $this->firstName = $user->first_name ?? $this->firstName;
            $this->lastName = $user->last_name ?? $this->lastName;
            $this->email = $user->email ?? $this->email;
            $this->phone = $user->phone ?? $this->phone;
        }
        // Load zip code from cart page session (priority over checkout form session)
        $cartZipCode = session('zip_code', '');
        
        if (!empty($cartZipCode)) {
            $this->zipCode = $cartZipCode;
            // Update checkout form session with cart zip code
            session(['checkout_form.zipCode' => $cartZipCode]);
        } elseif (empty($this->zipCode)) {
            // Fallback to default if no zip code is available
            $this->zipCode = '12345';
        }
    }

    public function updated($propertyName)
    {
        // Save form data to session as user types
        session(["checkout_form.{$propertyName}" => $this->$propertyName]);

        // Also save zip code to main session for consistency across components
        if ($propertyName === 'zipCode') {
            session(['zip_code' => $this->zipCode]);
        }
    }

    public function refreshZipCode()
    {
        // Refresh zip code from session (useful if updated from cart page)
        $cartZipCode = session('zip_code', '');
        if (!empty($cartZipCode) && $cartZipCode !== $this->zipCode) {
            $this->zipCode = $cartZipCode;
            session(['checkout_form.zipCode' => $cartZipCode]);
        }
    }



    public function render()
    {
        return view('livewire.checkout-form');
    }
}
