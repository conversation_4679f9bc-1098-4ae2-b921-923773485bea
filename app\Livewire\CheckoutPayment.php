<?php
namespace App\Livewire;

use Livewire\Component;
use Stripe\Stripe;
use Stripe\Charge;

class CheckoutPayment extends Component
{
    public $cart, $total, $tax, $stripe_token, $success, $error, $paid = false;

    protected $listeners = ['setStripeToken'];

    public function mount()
    {
        $this->cart = session('cart', []);
        $this->total = session('total', 0);
        $this->tax = session('tax', 0);
    }

    public function setStripeToken($token)
    {
        $this->stripe_token = $token;
        $this->payWithStripe();
    }

    public function payWithStripe()
    {
        $this->error = '';
        $this->success = '';
        try {
            Stripe::setApiKey(config('services.stripe.secret'));
            $charge = Charge::create([
                'amount' => $this->total * 100, // cents
                'currency' => 'usd',
                'source' => $this->stripe_token,
                'description' => 'Container Booking',
            ]);
            if ($charge->status === 'succeeded') {
                $this->paid = true;
                $this->success = 'Payment successful!';
                session()->forget(['cart', 'total', 'tax']);
            } else {
                dd($charge);
                $this->error = 'Payment failed. Please try again.';
                $this->dispatch('stripePaymentFailed', 'Payment failed. Please try again.');
            }
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            $this->dispatch('stripePaymentFailed', $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.checkout-payment');
    }
}
