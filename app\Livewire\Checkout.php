<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Customer;
use App\Models\Container;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Auth;
use Stripe\Stripe;
use Stripe\Charge;

class Checkout extends Component
{
    public $cart = [];
    public $billing_address = '';
    public $billing_city = '';
    public $billing_state = '';
    public $billing_zip = '';
    public $billing_country = '';
    public $shipping_address = '';
    public $shipping_city = '';
    public $shipping_state = '';
    public $shipping_zip = '';
    public $shipping_country = '';
    public $stripe_token = '';
    public $delivery_costs = [];
    public $total = 0;
    public $tax = 0;
    public $paid = false;
    public $error = '';
    public $success = '';
    public $stripe_transaction_id = '';

    protected $listeners = ['setStripeToken'];

    public function mount()
    {
        $this->cart = session('cart', []);
        // Prefill billing/shipping if user is logged in (optional)
        if (Auth::check()) {
            $user = Auth::user();
            $this->billing_address = $user->billing_address ?? '';
            $this->billing_city = $user->billing_city ?? '';
            $this->billing_state = $user->billing_state ?? '';
            $this->billing_zip = $user->billing_zip ?? '';
            $this->billing_country = $user->billing_country ?? '';
            $this->shipping_address = $user->shipping_address ?? '';
            $this->shipping_city = $user->shipping_city ?? '';
            $this->shipping_state = $user->shipping_state ?? '';
            $this->shipping_zip = $user->shipping_zip ?? '';
            $this->shipping_country = $user->shipping_country ?? '';
        }
    }

    public function calculateDelivery()
    {
        $this->delivery_costs = [];
        $totalDelivery = 0;
        foreach ($this->cart as $item) {
            $container = Container::with('warehouse', 'size')->find($item['id']);
            if (!$container || !$container->warehouse) {
                $this->delivery_costs[$item['id']] = 0;
                continue;
            }
            $origin = urlencode($container->warehouse->location);
            $destination = urlencode($this->shipping_address . ', ' . $this->shipping_city . ', ' . $this->shipping_state . ', ' . $this->shipping_zip . ', ' . $this->shipping_country);
            $apiKey = config('services.googlemaps.key');
            $url = "https://maps.googleapis.com/maps/api/distancematrix/json?origins={$origin}&destinations={$destination}&key={$apiKey}";
            $response = Http::get($url);
            $distance_km = 0;
            if ($response->ok() && isset($response['rows'][0]['elements'][0]['distance']['value'])) {
                $distance_km = $response['rows'][0]['elements'][0]['distance']['value'] / 1000;
            }
            $cost_per_km = $container->size ? $container->size->cost_per_km : 2.5;
            $delivery = round($distance_km * $cost_per_km, 2);
            $this->delivery_costs[$item['id']] = $delivery;
            $totalDelivery += $delivery;
        }
        $subtotal = collect($this->cart)->sum(function($item) {
            return $item['type'] === 'Rental' ? $item['price'] * $item['rentalMonths'] : $item['price'];
        });
        $this->tax = round(($subtotal + $totalDelivery) * 0.13, 2);
        $this->total = round($subtotal + $totalDelivery + $this->tax, 2);
        session([
            'cart' => $this->cart,
            'total' => $this->total,
            'tax' => $this->tax,
        ]);
        return redirect()->route('checkout.payment');
    }

    public function setStripeToken($token)
    {
        $this->stripe_token = $token;
        $this->payWithStripe();
    }

    public function payWithStripe()
    {
        $this->error = '';
        $this->success = '';
        try {

            Stripe::setApiKey(config('services.stripe.secret'));
            $charge = Charge::create([
                'amount' => $this->total * 100, // cents
                'currency' => 'usd',
                'source' => $this->stripe_token,
                'description' => 'Container Booking',
            ]);
            if ($charge->status === 'succeeded') {
                $this->stripe_transaction_id = $charge->id;
                $this->storeOrder();
                $this->paid = true;
                $this->success = 'Payment successful!';
            } else {
                $this->error = 'Payment failed. Please try again.';
                $this->dispatchBrowserEvent('stripePaymentFailed', 'Payment failed. Please try again.');
            }
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            $this->dispatchBrowserEvent('stripePaymentFailed', $e->getMessage());
        }
    }

    public function storeOrder()
    {
        foreach ($this->cart as $item) {
            Customer::create([
                'billing_address' => $this->billing_address,
                'billing_city' => $this->billing_city,
                'billing_state' => $this->billing_state,
                'billing_zip' => $this->billing_zip,
                'billing_country' => $this->billing_country,
                'shipping_address' => $this->shipping_address,
                'shipping_city' => $this->shipping_city,
                'shipping_state' => $this->shipping_state,
                'shipping_zip' => $this->shipping_zip,
                'shipping_country' => $this->shipping_country,
                'container_id' => $item['id'],
                'subtotal' => $item['type'] === 'Rental' ? $item['price'] * $item['rentalMonths'] : $item['price'],
                'delivery_cost' => $this->delivery_costs[$item['id']] ?? 0,
                'tax_amount' => $this->tax,
                'total' => $this->total,
                'stripe_transaction_id' => $this->stripe_transaction_id, // fill with Stripe charge id if needed
            ]);
        }
    }

    public function render()
    {
        return view('livewire.checkout');
    }
}
