<?php

namespace App\Livewire;

use Livewire\Component;
use Stripe\Stripe;
use Stripe\Checkout\Session;

class CheckoutSummary extends Component
{
    public $cart = [];
    public $subtotal = 0;
    public $deliveryCost = 0;
    public $tax = 0;
    public $total = 0;
    public $agreeToTerms = false;
    public $isFormComplete = false;

    public function mount()
    {
        $this->cart = session('cart', []);
        $this->subtotal = session('subtotal', 0);
        $this->deliveryCost = session('delivery_cost', 0);
        $this->tax = session('tax', 0);
        $this->total = session('total', 0);
        $this->checkFormCompletion();
    }

    public function checkFormCompletion()
    {
        // Check if all required fields are filled
        $requiredFields = [
            'firstName', 'lastName', 'email', 'phone',
            'streetAddress', 'city', 'province', 'zipCode'
        ];

        $this->isFormComplete = true;
        foreach ($requiredFields as $field) {
            if (empty(session("checkout_form.{$field}"))) {
                $this->isFormComplete = false;
                break;
            }
        }
    }

    public function processPayment()
    {
        if (!$this->agreeToTerms) {
            $this->dispatch('show-alert', [
                'message' => 'Please agree to the terms and conditions.',
                'type' => 'warning'
            ]);
            return;
        }

        if (empty($this->cart)) {
            $this->dispatch('show-alert', [
                'message' => 'Your cart is empty!',
                'type' => 'warning'
            ]);
            return;
        }

        // if (!$this->isFormComplete) {
        //     $this->dispatch('show-alert', [
        //         'message' => 'Please complete all required information before proceeding.',
        //         'type' => 'warning'
        //     ]);
        //     return;
        // }

        // Create Stripe Checkout Session
        return $this->createStripeCheckoutSession();
    }

    private function createStripeCheckoutSession()
    {
        try {
            // Check if Stripe is configured
            $stripeSecret = config('services.stripe.secret');
            if (empty($stripeSecret) || $stripeSecret === 'sk_test_your_stripe_secret_key_here') {
                // For demo/testing purposes, simulate successful payment
                $this->dispatch('show-alert', [
                    'message' => 'Demo mode: Payment would be processed via Stripe. Please configure Stripe keys in .env file for production.',
                    'type' => 'info'
                ]);

                // Simulate successful payment redirect
                return redirect()->route('checkout.success', ['session_id' => 'demo_' . uniqid()]);
            }

            // Set your Stripe secret key
            Stripe::setApiKey($stripeSecret);

            // Prepare line items for Stripe
            $lineItems = [];
            foreach ($this->cart as $item) {
                $lineItems[] = [
                    'price_data' => [
                        'currency' => 'usd',
                        'product_data' => [
                            'name' => $item['name'],
                            'description' => $item['size'] . 'ft ' . $item['category'] . ' Container - ' . $item['type'],
                        ],
                        'unit_amount' => (int)($item['price'] * 100), // Convert to cents
                    ],
                    'quantity' => 1,
                ];
            }

            // Add delivery as a separate line item
            if ($this->deliveryCost > 0) {
                $lineItems[] = [
                    'price_data' => [
                        'currency' => 'usd',
                        'product_data' => [
                            'name' => 'Delivery Fee',
                            'description' => 'Professional delivery service',
                        ],
                        'unit_amount' => (int)($this->deliveryCost * 100),
                    ],
                    'quantity' => 1,
                ];
            }

            // Add tax as a separate line item
            if ($this->tax > 0) {
                $lineItems[] = [
                    'price_data' => [
                        'currency' => 'usd',
                        'product_data' => [
                            'name' => 'Tax (13%)',
                            'description' => 'Sales tax',
                        ],
                        'unit_amount' => (int)($this->tax * 100),
                    ],
                    'quantity' => 1,
                ];
            }

            // Create Stripe Checkout Session
            $session = Session::create([
                'payment_method_types' => ['card'],
                'line_items' => $lineItems,
                'mode' => 'payment',
                'success_url' => route('checkout.success') . '?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => route('checkout'),
                'customer_email' => session('checkout_form.email'),
                'metadata' => [
                    'cart_count' => count($this->cart),
                    'delivery_cost' => $this->deliveryCost,
                    'tax' => $this->tax,
                ],
            ]);

            // Store session ID for later verification
            session(['stripe_session_id' => $session->id]);

            // Redirect to Stripe Checkout
            return redirect($session->url);

        } catch (\Exception $e) {
            $this->dispatch('show-alert', [
                'message' => 'Error creating payment session: ' . $e->getMessage(),
                'type' => 'error'
            ]);
        }
    }

    public function render()
    {
        $this->checkFormCompletion();
        return view('livewire.checkout-summary');
    }
}
