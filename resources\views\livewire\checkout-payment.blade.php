<div class="container my-4">
    <div class="row">
        <div class="col-md-6 offset-md-3">
            <div class="card p-3">
                <h5>Order Summary</h5>
                @if($cart)
                    <ul class="list-group mb-2">
                        @foreach($cart as $item)
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="fw-bold">{{ $item['name'] }}</div>
                                    <div class="small">{{ $item['type'] }} @if($item['type']==='Rental') ({{ $item['rentalMonths'] }} mo) @endif</div>
                                    <div class="small">Warehouse: {{ $item['warehouse'] }}</div>
                                </div>
                                <div>
                                    <span class="fw-bold">${{ $item['price'] }}</span>
                                </div>
                            </li>
                        @endforeach
                    </ul>
                    <div class="mb-2">
                        <div>Tax: ${{ $tax }}</div>
                        <div class="fw-bold">Total: ${{ $total }}</div>
                    </div>
                @else
                    <div class="text-muted">Cart is empty.</div>
                @endif

                @if($success)
                    <div class="alert alert-success mt-3">{{ $success }}</div>
                @endif
                @if($error)
                    <div class="alert alert-danger mt-3">{{ $error }}</div>
                @endif

                @if(!$paid)
                    <form id="stripe-form">
                        <div id="card-element" class="form-control mb-2"></div>
                        <div id="card-errors" class="text-danger mb-2"></div>
                        <button id="stripe-submit" class="btn btn-success w-100" type="submit" >Pay with Stripe</button>
                    </form>
                @endif
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script>
function mountStripeCard() {
    if (!$('#card-element').length) return;
    if (window.stripeCardMounted) return;
    window.stripeCardMounted = true;
    const stripe = Stripe('{{ config('services.stripe.public') }}');
    const elements = stripe.elements();
    const card = elements.create('card');
    card.mount('#card-element');
    card.on('change', function(event) {
        const displayError = $('#card-errors');
        if (event.error) {
            displayError.text(event.error.message);
        } else {
            displayError.text('');
        }
    });
    $('#stripe-form').off('submit').on('submit', function(event) {
        event.preventDefault();
        $('#stripe-submit').prop('disabled', true);
        stripe.createToken(card).then(function(result) {
            if (result.error) {
                $('#card-errors').text(result.error.message);
                $('#stripe-submit').prop('disabled', false);
            } else {
                window.livewire.emit('setStripeToken', result.token.id);
            }
        });
    });
    window.livewire.on('stripePaymentFailed', function(message) {
        $('#stripe-submit').prop('disabled', false);
        $('#card-errors').text(message);
    });
}
document.addEventListener('livewire:load', function() {
    mountStripeCard();
    if (window.livewire) {
        window.livewire.hook('message.processed', function() {
            window.stripeCardMounted = false;
            mountStripeCard();
        });
    }
});
</script>
@endpush
