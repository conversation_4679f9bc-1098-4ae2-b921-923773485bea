<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Container;
use App\Models\Category;
use App\Models\Size;
use App\Models\Warehouse;

class ContainerCatalog extends Component
{
    public $sizes = [];
    public $categories = [];
    public $types = ['rental', 'purchase'];
    public $warehouses = [];
    public $containers = [];

    // Filters
    public $filterSize = [];
    public $filterCategory = [];
    public $filterType = [];
    public $filterWarehouse = '';
    public $filterSearch = '';
    public $filterSort = '';
    public $priceRange = 50000;

    // Cart
    public $cart = [];
    public $selectedType = [];
    public $selectedDuration = [];

    // Dynamic pricing
    public $containerPrices = [];

    public function mount()
    {
        $this->sizes = Size::pluck('size')->toArray();
        $this->categories = Category::all();
        $this->warehouses = Warehouse::pluck('location')->toArray();
        $this->loadContainers();
        $this->initializeContainerPrices();
    }

    public function initializeContainerPrices()
    {
        foreach ($this->containers as $container) {
            $this->containerPrices[$container->id] = [
                'rental' => $container->rental_price,
                'purchase' => $container->purchase_price,
                'rental_total' => $container->rental_price
            ];
        }
    }

    public function loadContainers()
    {
        $query = Container::with(['size', 'category', 'warehouse']);

        // Size filter
        if (!empty($this->filterSize)) {
            $query->whereHas('size', function($q) {
                $q->whereIn('size', $this->filterSize);
            });
        }

        // Category filter
        if (!empty($this->filterCategory)) {
            $query->whereHas('category', function($q) {
                $q->whereIn('id', $this->filterCategory);
            });
        }

        // Type filter
        if (!empty($this->filterType)) {
            $query->where(function($q) {
                $q->whereIn('type', $this->filterType)
                  ->orWhere('type', 'both');
            });
        } else {
            // If no type filter is selected, show all containers including 'both' type
            $query->where(function($q) {
                $q->whereIn('type', ['rental', 'purchase', 'both']);
            });
        }

        // Warehouse filter
        if ($this->filterWarehouse) {
            $query->whereHas('warehouse', function($q) {
                $q->where('location', $this->filterWarehouse);
            });
        }

        // Search filter
        if ($this->filterSearch) {
            $query->where(function($q) {
                $q->where('title', 'like', '%'.$this->filterSearch.'%')
                  ->orWhere('description', 'like', '%'.$this->filterSearch.'%');
            });
        }

        // Price range filter
        if ($this->priceRange) {
            $query->where(function($q) {
                $q->where('rental_price', '<=', $this->priceRange)
                  ->orWhere('purchase_price', '<=', $this->priceRange);
            });
        }

        // Sorting
        if ($this->filterSort === 'price-asc') {
            $query->orderBy('rental_price', 'asc');
        } elseif ($this->filterSort === 'price-desc') {
            $query->orderBy('rental_price', 'desc');
        } elseif ($this->filterSort === 'size') {
            $query->orderBy('size_id', 'asc');
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $this->containers = $query->get();
        $this->initializeContainerPrices();
    }

        public function updated($property)
    {
        if (str_starts_with($property, 'filter') || $property === 'priceRange') {
            $this->loadContainers();
        }

        // Handle type selection changes
        if (str_starts_with($property, 'selectedType.')) {
            $containerId = str_replace('selectedType.', '', $property);
            $this->updateContainerPrice($containerId);
        }

        // Handle duration selection changes
        if (str_starts_with($property, 'selectedDuration.')) {
            $containerId = str_replace('selectedDuration.', '', $property);
            $this->updateContainerPrice($containerId);
        }
    }

    public function updateContainerPrice($containerId)
    {
        $container = $this->containers->firstWhere('id', $containerId);
        if (!$container) return;

        $selectedType = $this->selectedType[$containerId] ?? 'Rental';
        $selectedDuration = (int)($this->selectedDuration[$containerId] ?? 1);

        if ($selectedType === 'Rental') {
            if (!isset($this->containerPrices[$containerId])) {
                $this->containerPrices[$containerId] = [];
            }
            $this->containerPrices[$containerId]['rental_total'] = $container->rental_price * $selectedDuration;
        }
    }

    public function addToCart($containerId, $type = 'Rental', $months = 1)
    {
        $container = Container::with(['size', 'category', 'warehouse'])->find($containerId);
        if (!$container) return;

        $key = $containerId.'-'.$type.'-'.$months;

        // Calculate the correct price based on type and duration
        if ($type === 'Rental') {
            $price = $container->rental_price * $months;
        } else {
            $price = $container->purchase_price;
        }

                $this->cart[$key] = [
                'id' => $container->id,
                'name' => $container->title,
                'image' => $container->image,
                'warehouse' => $container->warehouse ? $container->warehouse->location : '',
                'qty' => 1,
                'type' => $type,
                'price' => $price,
                'rentalMonths' => $type === 'Rental' ? $months : null,
                'size' => $container->size ? $container->size->size : '',
                'deliveryRate' => $container->size ? $container->size->cost_per_km : 2.5,
                'deliveryCost' => $container->delivery_cost ?? 0,
                'category' => $container->category ? $container->category->title : '',
            ];

        $this->dispatch('cart-updated', ['count' => count($this->cart)]);
    }

    public function removeFromCart($key)
    {
        unset($this->cart[$key]);
        $this->dispatch('cart-updated', ['count' => count($this->cart)]);
    }

    public function clearCart()
    {
        $this->cart = [];
        $this->dispatch('cart-updated', ['count' => 0]);
    }

    public function checkout()
    {
        if (empty($this->cart)) {
            $this->dispatch('show-alert', ['message' => 'Your cart is empty!', 'type' => 'warning']);
            return;
        }

        session(['cart' => $this->cart]);
        return redirect()->route('cart');
    }

    public function render()
    {
        return view('livewire.container-catalog');
    }
}
