<?php

namespace App\Http\Controllers\pages;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Container;
use App\Models\Category;
use App\Models\Size;
use App\Models\Warehouse;

class HomePage extends Controller
{
  public function index()
  {
    $containers = Container::with(['size', 'category', 'warehouse'])->get();
    $categories = Category::all();
    $sizes = Size::all();
    $warehouses = Warehouse::all();
    $types = ['Rental', 'Full Purchase', 'Both'];

    return view('front.index', [
      'containers' => $containers,
      'categories' => $categories,
      'sizes' => $sizes,
      'warehouses' => $warehouses,
      'types' => $types,
    ]);
  }
}
