<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Container extends Model
{
    protected $fillable = [
        'title',
        'description',
        'size_id',
        'category_id',
        'type',
        'rental_price',
        'purchase_price',
        'warehouse_id',
        'image',
        'delivery_cost',
    ];

    public function size()
    {
        return $this->belongsTo(Size::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function customers()
    {
        return $this->hasMany(Customer::class);
    }
}
