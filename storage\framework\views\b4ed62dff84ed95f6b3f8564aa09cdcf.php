<div>
    <!-- Delivery Calculator -->
    <div class="delivery-calculator">
        <div class="d-flex align-items-center mb-3">
            <i class="fas fa-calculator text-dark me-2"></i>
            <h5 class="mb-0">Delivery Calculator</h5>
        </div>

        <div class="row align-items-end">
            <div class="col-md-6">
                <label for="zipCode" class="form-label">Your Zip Code</label>
                <input type="text" class="form-control" id="zipCode" wire:model="zipCode"
                    placeholder="Enter your zip code">
            </div>
            <div class="col-md-6">
                <button class="btn btn-dark" wire:click="calculateDelivery">
                    Calculate Total Delivery
                </button>
            </div>
        </div>
        <small class="text-muted">Delivery costs are based on individual container rates</small>
    </div>

    <!-- Cart Items -->
    <!--[if BLOCK]><![endif]--><?php if(count($cart) > 0): ?>
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $cart; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="cart-item p-4 mb-3" data-cart-key="<?php echo e($key); ?>">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <img src="<?php echo e($item['image'] ?? 'https://via.placeholder.com/100'); ?>" class="img-fluid rounded"
                            alt="<?php echo e($item['name']); ?>" style="width: 100px; height: 100px; object-fit: cover;">
                    </div>

                    <div class="col-md-4">
                        <h6 class="mb-1"><?php echo e($item['name']); ?></h6>
                        <p class="text-muted small mb-2">
                            <?php echo e($item['description'] ?? 'Perfect for residential storage needs or small business inventory.'); ?>

                        </p>

                        <div class="mb-2">
                            <span class="tag-pill"><?php echo e($item['size']); ?>ft</span>
                            <span class="tag-pill"><?php echo e(strtolower($item['category'] ?? 'storage')); ?></span>
                            <span class="tag-pill"><?php echo e($item['rentalMonths'] ?? 1); ?>mo</span>
                            <span class="tag-pill">Delivery: $<?php echo e(number_format($item['deliveryCost'] ?? 0, 2)); ?></span>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-dark me-2"><?php echo e($item['type']); ?></span>
                            <!--[if BLOCK]><![endif]--><?php if($item['type'] === 'Rental'): ?>
                                <span class="badge bg-secondary"><?php echo e($item['rentalMonths']); ?>

                                    month<?php echo e($item['rentalMonths'] > 1 ? 's' : ''); ?></span>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    <div class="col-md-2 text-end">
                        <button class="btn btn-link text-danger p-0 mb-2"
                            wire:click="removeItem('<?php echo e($key); ?>')">
                            <i class="fas fa-trash"></i>
                        </button>

                        <div class="input-group cart-qty-controls mt-2">
                            <button class="btn btn-outline-secondary" type="button"
                                onclick="decrementQuantity('<?php echo e($key); ?>')">-</button>
                            <input type="text" class="form-control text-center" style="height:35px !important;"
                                value="<?php echo e($item['quantity'] ?? 1); ?>" readonly
                                data-quantity="<?php echo e($item['quantity'] ?? 1); ?>" style="background:#fff;">
                            <button class="btn btn-outline-secondary" type="button"
                                onclick="incrementQuantity('<?php echo e($key); ?>')">+</button>
                        </div>

                        <div class="fw-bold" data-unit-price="<?php echo e($item['unit_price'] ?? ($item['price'] ?? 0)); ?>"
                            data-total-price="<?php echo e($item['price']); ?>">
                            $<?php echo e(number_format($item['price'], 2)); ?>

                        </div>
                        <small class="text-muted">
                            <?php echo e($item['type'] === 'Rental' ? 'Rental cost' : 'Purchase cost'); ?>

                        </small>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Your cart is empty</h5>
            <p class="text-muted">Add some containers to get started!</p>
            <a href="<?php echo e(route('catalog')); ?>" class="btn btn-dark">
                Continue Shopping
            </a>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <style>
        .cart-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .tag-pill {
            display: inline-block;
            padding: 4px 8px;
            background: #f8f9fa;
            color: #495057;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 8px;
            margin-bottom: 4px;
        }

        .cart-qty-controls {
            max-width: 120px;
        }

        .cart-qty-controls .btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .cart-qty-controls input {
            text-align: center;
            background: white !important;
        }

        .delivery-calculator {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>

    <script>
        // Flag to prevent interference during delivery calculation
        let isCalculatingDelivery = false;

        //
        // ─── CART PAGE QUANTITY CONTROLS ─────────────────────────────────────────────
        //
        function incrementQuantity(itemKey) {
            const cartItem = document.querySelector(`[data-cart-key="${itemKey}"]`);
            if (!cartItem) return console.error('Cart item not found:', itemKey);

            const input = cartItem.querySelector('input[data-quantity]');
            const priceDiv = cartItem.querySelector('.fw-bold');
            const unitPrice = parseFloat(priceDiv.getAttribute('data-unit-price')) || 0;

            let qty = parseInt(input.getAttribute('data-quantity'), 10) || 1;
            qty++;

            // Update UI
            input.value = qty;
            input.setAttribute('data-quantity', qty);

            const totalPrice = unitPrice * qty;
            priceDiv.textContent = '$' + totalPrice.toLocaleString('en-US', {
                minimumFractionDigits: 2
            });
            priceDiv.setAttribute('data-total-price', totalPrice);

            updateCartTotals();
            // updateSummaryTotals();

            // if (typeof Livewire !== 'undefined') {
            //   Livewire.emit('incrementQuantity', itemKey);
            // }
        }

        function decrementQuantity(itemKey) {
            const cartItem = document.querySelector(`[data-cart-key="${itemKey}"]`);
            if (!cartItem) return console.error('Cart item not found:', itemKey);

            const input = cartItem.querySelector('input[data-quantity]');
            const priceDiv = cartItem.querySelector('.fw-bold');
            const unitPrice = parseFloat(priceDiv.getAttribute('data-unit-price')) || 0;

            let qty = parseInt(input.getAttribute('data-quantity'), 10) || 1;
            if (qty <= 1) return;

            qty--;
            input.value = qty;
            input.setAttribute('data-quantity', qty);

            const totalPrice = unitPrice * qty;
            priceDiv.textContent = '$' + totalPrice.toLocaleString('en-US', {
                minimumFractionDigits: 2
            });
            priceDiv.setAttribute('data-total-price', totalPrice);

            updateCartTotals();
            // updateSummaryTotals();

            // if (typeof Livewire !== 'undefined') {
            //     Livewire.emit('decrementQuantity', itemKey);
            // }
        }

        //
        // ─── SUMMARY PAGE QUANTITY CONTROLS ──────────────────────────────────────────
        //
        function incrementCheckoutQuantity(itemKey) {
            const row = document.querySelector(`.checkout-cart-item[data-cart-key="${itemKey}"]`);
            if (!row) return console.error('Summary row not found:', itemKey);

            const input = row.querySelector('.checkout-qty-input');
            const unitPrice = parseFloat(input.getAttribute('data-unit-price')) || 0;

            let qty = parseInt(input.getAttribute('data-quantity'), 10) || 1;
            qty++;

            input.value = qty;
            input.setAttribute('data-quantity', qty);

            const linePriceEl = row.querySelector('.checkout-item-price');
            const lineTotal = unitPrice * qty;

            linePriceEl.textContent = '$' + lineTotal.toLocaleString('en-US', {
                minimumFractionDigits: 2
            });
            linePriceEl.setAttribute('data-price', lineTotal);

            // updateSummaryTotals();
            updateCartTotals();

            // if (typeof Livewire !== 'undefined') {
            //     Livewire.emit('incrementQuantity', itemKey);
            // }
        }

        function decrementCheckoutQuantity(itemKey) {
            const row = document.querySelector(`.checkout-cart-item[data-cart-key="${itemKey}"]`);
            if (!row) return console.error('Summary row not found:', itemKey);

            const input = row.querySelector('.checkout-qty-input');
            const unitPrice = parseFloat(input.getAttribute('data-unit-price')) || 0;

            let qty = parseInt(input.getAttribute('data-quantity'), 10) || 1;
            if (qty <= 1) return;

            qty--;
            input.value = qty;
            input.setAttribute('data-quantity', qty);

            const linePriceEl = row.querySelector('.checkout-item-price');
            const lineTotal = unitPrice * qty;

            linePriceEl.textContent = '$' + lineTotal.toLocaleString('en-US', {
                minimumFractionDigits: 2
            });
            linePriceEl.setAttribute('data-price', lineTotal);

            // updateSummaryTotals();
            updateCartTotals();

            // if (typeof Livewire !== 'undefined') {
            //     Livewire.emit('decrementQuantity', itemKey);
            // }
        }

        //
        // ─── TOTALS UPDATERS ──────────────────────────────────────────────────────────
        //
        function updateCartTotals() {
            let subtotal = 0;

            document.querySelectorAll('[data-cart-key]').forEach(item => {
                const lineTotal = parseFloat(
                    item.querySelector('.fw-bold').getAttribute('data-total-price')
                ) || 0;
                subtotal += lineTotal;
            });

            const delivery = parseFloat(document.querySelector('.order-summary').getAttribute('data-delivery')) || 0;
            const tax = (subtotal + delivery) * 0.13;
            const total = subtotal + delivery + tax;
            console.log('subtotal', subtotal, 'delivery', delivery, 'tax', tax, 'total', total);

            document.getElementById('subtotal').innerHTML = '$' + subtotal.toFixed(2);
            document.getElementById('deliverysummry').innerHTML = '$' + delivery.toFixed(2);
            document.getElementById('taxsummary').innerHTML = '$' + tax.toFixed(2);
            document.getElementById('totalsummary').innerHTML = '$' + total.toFixed(2);
            document.getElementById('proceedToCheckoutbtn').innerHTML = 'Pay $' + total.toFixed(2);
        }

        // Function to update only delivery, tax, and total using backend values
        function updateDeliveryOnly(data) {
            console.log('🔧 updateDeliveryOnly called with data:', data);

            // Check if data is properly structured
            if (!data) {
                console.error('❌ No data passed to updateDeliveryOnly');
                return;
            }

            // Handle array format from Livewire (data comes as array)
            let actualData = data;
            if (Array.isArray(data) && data.length > 0) {
                actualData = data[0];
                console.log('📦 Data was array, using first element:', actualData);
            }

            if (!actualData || typeof actualData !== 'object') {
                console.error('❌ Invalid data structure:', actualData);
                return;
            }

            const { delivery, tax, total } = actualData;
            console.log('📊 Extracted values - delivery:', delivery, 'tax:', tax, 'total:', total);

            // Update delivery only and recalculate tax/total based on current subtotal
            if (typeof delivery !== 'undefined') {
                const deliveryEl = document.getElementById('deliverysummry');
                const subtotalEl = document.getElementById('subtotal');

                if (deliveryEl && subtotalEl) {
                    // Get current subtotal (preserve existing cart calculations)
                    const currentSubtotalText = subtotalEl.innerHTML.replace('$', '').replace(',', '');
                    const currentSubtotal = parseFloat(currentSubtotalText) || 0;
                    console.log('💰 Current subtotal preserved:', currentSubtotal);

                    // Update delivery
                    const oldDeliveryValue = deliveryEl.innerHTML;
                    const newDeliveryValue = '$' + Number(delivery).toFixed(2);
                    deliveryEl.innerHTML = newDeliveryValue;
                    console.log('✅ Delivery updated from', oldDeliveryValue, 'to', newDeliveryValue);

                    // Recalculate tax based on current subtotal + new delivery
                    const newTax = (currentSubtotal + Number(delivery)) * 0.13;
                    const newTotal = currentSubtotal + Number(delivery) + newTax;

                    // Update tax
                    const taxEl = document.getElementById('taxsummary');
                    if (taxEl) {
                        const oldTaxValue = taxEl.innerHTML;
                        const newTaxValue = '$' + newTax.toFixed(2);
                        taxEl.innerHTML = newTaxValue;
                        console.log('✅ Tax recalculated from', oldTaxValue, 'to', newTaxValue);
                    }

                    // Update total
                    const totalEl = document.getElementById('totalsummary');
                    const btnEl = document.getElementById('proceedToCheckoutbtn');

                    if (totalEl) {
                        const oldTotalValue = totalEl.innerHTML;
                        const newTotalValue = '$' + newTotal.toFixed(2);
                        totalEl.innerHTML = newTotalValue;
                        console.log('✅ Total recalculated from', oldTotalValue, 'to', newTotalValue);
                    }

                    if (btnEl) {
                        const oldBtnValue = btnEl.innerHTML;
                        const newBtnValue = 'Pay $' + newTotal.toFixed(2);
                        btnEl.innerHTML = newBtnValue;
                        console.log('✅ Button updated from', oldBtnValue, 'to', newBtnValue);
                    }

                    console.log('🧮 Calculation summary:');
                    console.log('   Subtotal (preserved):', currentSubtotal);
                    console.log('   Delivery (new):', Number(delivery));
                    console.log('   Tax (recalculated):', newTax);
                    console.log('   Total (recalculated):', newTotal);

                } else {
                    console.error('❌ Required elements not found - deliverysummry:', !!deliveryEl, 'subtotal:', !!subtotalEl);
                }
            } else {
                console.error('❌ Delivery value not provided in data');
            }
            updateCartTotals();
            console.log('🎉 updateDeliveryOnly completed successfully');
        }
        //
        // ─── INITIALIZATION ───────────────────────────────────────────────────────────
        //
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 DOM Content Loaded - initializing cart');
            updateCartTotals();

            // Add click listener to delivery button for debugging
            const deliveryBtn = document.querySelector('button[wire\\:click="calculateDelivery"]');
            if (deliveryBtn) {
                deliveryBtn.addEventListener('click', function() {
                    console.log('🖱️ Calculate Delivery button clicked');
                    const zipCode = document.getElementById('zipCode')?.value;
                    console.log('📮 Zip code value:', zipCode);

                    // Set flag to prevent interference
                    isCalculatingDelivery = true;
                    console.log('🚩 Set isCalculatingDelivery flag to true');
                });
                console.log('✅ Delivery button click listener added');
            } else {
                console.error('❌ Delivery button not found');
            }
        });

        // Listen specifically for delivery calculation events
        document.addEventListener('livewire:load', function() {
            console.log('🚀 Livewire:load event fired');
            if (typeof Livewire !== 'undefined') {
                console.log('✅ Livewire is available, setting up delivery-calculated listener');
                Livewire.on('delivery-calculated', function(data) {
                    console.log('🎯 delivery-calculated event received with data:', data);
                    console.log('📋 Data type:', typeof data, 'Data keys:', Object.keys(data || {}));
                    updateDeliveryOnly(data);

                    // Reset flag after delivery calculation is complete
                    setTimeout(() => {
                        isCalculatingDelivery = false;
                        console.log('🚩 Reset isCalculatingDelivery flag to false');
                    }, 100);
                });
                console.log('✅ delivery-calculated listener registered');
            } else {
                console.error('❌ Livewire is not available');
            }
        });

        // Also try listening on DOMContentLoaded in case livewire:load doesn't fire
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOMContentLoaded event fired');
            if (typeof Livewire !== 'undefined') {
                console.log('✅ Livewire available on DOMContentLoaded, setting up backup listener');
                Livewire.on('delivery-calculated', function(data) {
                    console.log('🎯 [BACKUP] delivery-calculated event received:', data);
                    updateDeliveryOnly(data);

                    // Reset flag after delivery calculation is complete
                    setTimeout(() => {
                        isCalculatingDelivery = false;
                        console.log('🚩 [BACKUP] Reset isCalculatingDelivery flag to false');
                    }, 100);
                });
            }
        });

        document.addEventListener('livewire:update', () => {
            console.log('🔄 livewire:update event fired');
            setTimeout(() => {
                console.log('🔍 Checking if should update cart totals:');
                console.log('   - isCalculatingDelivery flag:', isCalculatingDelivery);

                if (isCalculatingDelivery) {
                    console.log('⏭️ Skipping updateCartTotals() - delivery calculation in progress');
                    return;
                }

                console.log('✅ Calling updateCartTotals() from livewire:update');
                updateCartTotals();
            }, 50);
        });
    </script>

</div>
<?php /**PATH C:\xampp\htdocs\containers\resources\views/livewire/cart-items.blade.php ENDPATH**/ ?>