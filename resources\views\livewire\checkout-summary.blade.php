<div class="order-summary sticky-top" style="top: 20px;">
    <h5 class="mb-3">Order Summary</h5>
    @if(count($cart) > 0)
        @foreach($cart as $key => $item)
            <div class="d-flex justify-content-between align-items-start mb-3">
                <div>
                    <h6 class="mb-1">{{ $item['name'] }}</h6>
                    <div class="small text-muted">
                        <span class="badge bg-secondary me-1">{{ $item['size'] }}ft</span>
                        @if($item['type'] === 'Rental')
                            <span class="badge bg-secondary me-1">{{ $item['rentalMonths'] }}mo</span>
                        @endif
                        <span class="badge bg-secondary">Delivery: ${{ number_format($item['deliveryCost'] ?? 0, 2) }}</span>
                    </div>
                </div>
                <div class="text-end">
                    <span class="fw-bold">${{ number_format($item['price'] * $item['qty'], 2) }}</span>
                </div>
            </div>
        @endforeach
    @else
        <div class="text-center py-3">
            <p class="text-muted">No items in cart</p>
        </div>
    @endif

    <hr>

    <div class="d-flex justify-content-between mb-2">
        <span>Subtotal</span>
        <span>${{ number_format($subtotal, 2) }}</span>
    </div>

    <div class="d-flex justify-content-between mb-2">
        <span>Delivery</span>
        <span>${{ number_format($deliveryCost, 2) }}</span>
    </div>

    <div class="d-flex justify-content-between mb-3">
        <span>Tax (13%)</span>
        <span>${{ number_format($tax, 2) }}</span>
    </div>

    <hr>

    <div class="d-flex justify-content-between mb-3">
        <strong>Total</strong>
        <strong class="h5">${{ number_format($total, 2) }}</strong>
    </div>

    <div class="form-check mb-3">
        <input class="form-check-input" type="checkbox" wire:model="agreeToTerms" id="terms">
        <label class="form-check-label" for="terms">
            I agree to the terms and conditions
        </label>
    </div>

    <button class="btn btn-dark w-100 mb-3" wire:click="processPayment" {{ ($total <= 0 ) ? 'disabled' : '' }}>
        <i class="fab fa-stripe me-2"></i>Pay with Stripe ${{ number_format($total, 2) }}
    </button>

    @if($total <= 0)
        <p class="text-muted small text-center">Please add items to cart and calculate delivery costs</p>
    {{-- @elseif(!$isFormComplete)
        <p class="text-muted small text-center">Please complete all required information before proceeding</p> --}}
    @endif

    <div class="mt-4">
        <h6 class="mb-2">Security & Services:</h6>
        <ul class="list-unstyled small text-muted">
            <li><i class="fas fa-shield-alt text-dark me-2"></i>Secure 256-bit SSL encryption</li>
            <li><i class="fas fa-truck text-dark me-2"></i>Professional delivery included</li>
            <li><i class="fas fa-check-circle text-dark me-2"></i>30-day satisfaction guarantee</li>
        </ul>
    </div>
</div>
