<?php

namespace App\Livewire;

use Livewire\Component;

class CartSummary extends Component
{
    public $cart = [];
    public $subtotal = 0;
    public $deliveryCost = 0;
    public $tax = 0;
    public $total = 0;
    public $agreeToTerms = false;

    public function mount()
    {
        $this->cart = session('cart', []);
        $this->deliveryCost = session('delivery_cost', 0);
        $this->calculateTotals();
    }

    public function updated($property)
    {
        // Listen for cart updates and recalculate totals
        if ($property === 'cart' || $property === 'deliveryCost') {
            $this->calculateTotals();
        }
    }

    public function hydrate()
    {
        // Refresh delivery cost from session when component is hydrated
        $this->deliveryCost = session('delivery_cost', 0);
        $this->calculateTotals();
    }

    protected $listeners = [
        'delivery-calculated' => 'refreshTotals',
        'cart-updated' => 'refreshCart'
    ];

    public function refreshTotals()
    {
        $this->deliveryCost = session('delivery_cost', 0);
        $this->calculateTotals();
    }

    public function refreshCart()
    {
        $this->cart = session('cart', []);
        $this->calculateTotals();
    }

    public function calculateTotals()
    {
        $this->subtotal = collect($this->cart)->sum(function($item) {
            return $item['type'] === 'Rental' ? $item['price'] * $item['rentalMonths'] : $item['price'];
        });

        $this->tax = round(($this->subtotal + $this->deliveryCost) * 0.13, 2);
        $this->total = round($this->subtotal + $this->deliveryCost + $this->tax, 2);
    }

    public function proceedToCheckout()
    {
        if (empty($this->cart)) {
            $this->dispatch('show-alert', [
                'message' => 'Your cart is empty!',
                'type' => 'warning'
            ]);
            return;
        }

        if (!$this->agreeToTerms) {
            $this->dispatch('show-alert', [
                'message' => 'Please agree to the terms and conditions.',
                'type' => 'warning'
            ]);
            return;
        }

        if ($this->deliveryCost <= 0) {
            $this->dispatch('show-alert', [
                'message' => 'Please calculate delivery costs first.',
                'type' => 'warning'
            ]);
            return;
        }

        session([
            'cart' => $this->cart,
            'delivery_cost' => $this->deliveryCost,
            'subtotal' => $this->subtotal,
            'tax' => $this->tax,
            'total' => $this->total
        ]);

        return redirect()->route('checkout');
    }

    public function render()
    {
        return view('livewire.cart-summary');
    }
}
