<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Container;

class CartItems extends Component
{
    public $cart = [];
    public $zipCode = '';
    public $deliveryCost = 0;

    public function mount()
    {
        $this->cart = session('cart', []);
    }

    public function calculateDelivery()
    {
        if (empty($this->zipCode)) {
            $this->dispatch('show-alert', [
                'message' => 'Please enter your zip code to calculate delivery.',
                'type' => 'warning'
            ]);
            return;
        }

        // Use the container's delivery_cost from the cart
        $this->deliveryCost = round(collect($this->cart)->sum(function($item) {
            return $item['deliveryCost'] ?? 0; // Use container's delivery_cost
        }), 2);

        // Store delivery cost in session for CartSummary component
        session(['delivery_cost' => $this->deliveryCost]);

        // Calculate subtotal, tax, and total for summary update
        $subtotal = collect($this->cart)->sum(function($item) {
            return $item['type'] === 'Rental' ? $item['price'] * ($item['rentalMonths'] ?? 1) : $item['price'];
        });
        $tax = round(($subtotal + $this->deliveryCost) * 0.13, 2);
        $total = round($subtotal + $this->deliveryCost + $tax, 2);

        // Emit event to update CartSummary component and JS
        $this->dispatch('delivery-calculated', [
            'delivery' => $this->deliveryCost,
            'tax' => $tax,
            'total' => $total,
        ]);

        $this->dispatch('show-alert', [
            'message' => 'Delivery cost calculated successfully! Total delivery cost: $' . number_format($this->deliveryCost, 2),
            'type' => 'success'
        ]);
    }



    public function removeItem($key)
    {
        unset($this->cart[$key]);
        session(['cart' => $this->cart]);

        $this->dispatch('cart-updated', ['count' => count($this->cart)]);
        $this->dispatch('show-alert', [
            'message' => 'Item removed from cart.',
            'type' => 'success'
        ]);
    }



    public function render()
    {
        return view('livewire.cart-items');
    }
}
