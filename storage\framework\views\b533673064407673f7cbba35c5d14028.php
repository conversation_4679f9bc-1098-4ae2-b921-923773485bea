<div class="order-summary sticky-top" style="top: 20px;">
    <h5 class="mb-3">Order Summary</h5>

    <div class="d-flex justify-content-between mb-2">
        <span>Subtotal</span>
        <span id="SubtotalSummary">$<?php echo e(number_format($subtotal, 2)); ?></span>
    </div>

    <div class="d-flex justify-content-between mb-2">
        <span>Delivery</span>
        <span id="DeliverySummary">$<?php echo e(number_format($deliveryCost, 2)); ?></span>
    </div>

    <div class="d-flex justify-content-between mb-3">
        <span>Tax (13%)</span>
        <span id="TaxSummary">$<?php echo e(number_format($tax, 2)); ?></span>
    </div>

    <hr>

    <div class="d-flex justify-content-between mb-3">
        <strong>Total</strong>
        <strong class="h5" id="TotalSummary">$<?php echo e(number_format($total, 2)); ?></strong>
    </div>

    <div class="form-check mb-3">
        <input class="form-check-input" type="checkbox" wire:model="agreeToTerms" id="terms">
        <label class="form-check-label" for="terms">
            I agree to the terms and conditions
        </label>
    </div>

    <button class="btn btn-primary w-100 mb-3" wire:click="proceedToCheckout"
        <?php echo e($deliveryCost <= 0 ? 'disabled' : ''); ?>>
        Pay <span id="TotalBtnSummary"> $<?php echo e(number_format($total, 2)); ?> </span>
    </button>

    <!--[if BLOCK]><![endif]--><?php if($deliveryCost <= 0): ?>
        <p class="text-muted small text-center">Please calculate delivery costs to continue.</p>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <div class="mt-4">
        <h6 class="mb-2">Included Services:</h6>
        <ul class="list-unstyled small text-muted">
            <li><i class="fas fa-check text-success me-2"></i>Free consultation included</li>
            <li><i class="fas fa-check text-success me-2"></i>Professional delivery team</li>
            <li><i class="fas fa-check text-success me-2"></i>Installation support available</li>
        </ul>
    </div>
</div>

<script>
    // document.addEventListener('livewire:init', () => {
    //     Livewire.on('cart-updated-CartItems', (data) => {
    //         // Update summary values when cart changes
    //         // console.log(data)
    //         document.getElementById('SubtotalSummary').textContent = '$' + data[0].subtotal.toFixed(2);
    //         document.getElementById('TaxSummary').textContent = '$' + data[0].tax.toFixed(2);
    //         document.getElementById('TotalSummary').textContent = '$' + data[0].total.toFixed(2);
    //         document.getElementById('TotalBtnSummary').textContent = '$' + data[0].total.toFixed(2);
    //         // console.log(
    //         //     document.getElementById('SubtotalSummary').textContent,
    //         // document.getElementById('TaxSummary').textContent,
    //         // document.getElementById('TotalSummary').textContent,
    //         // document.getElementById('TotalBtnSummary').textContent,
    //         // )
    //     });
    // });
</script>   
<?php /**PATH C:\xampp\htdocs\containers\resources\views/livewire/cart-summary.blade.php ENDPATH**/ ?>